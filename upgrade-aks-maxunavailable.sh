#!/bin/bash

echo "=== Upgrade AKS Node (MaxUnavailable Method) ==="

# Variables
AKS_CLUSTER_NAME="myAKSCluster"
AKS_RESOURCE_GROUP="myResourceGroup"
AKS_NODE_POOL_NAME="nodepool1"
NEW_VM_SIZE="Standard_D4s_v3"

echo "AKS Cluster: $AKS_CLUSTER_NAME"
echo "AKS Resource Group: $AKS_RESOURCE_GROUP"
echo "Current Node Pool: $AKS_NODE_POOL_NAME"
echo "New VM Size: $NEW_VM_SIZE (4 vCPUs, 16 GB RAM)"
echo

# Show current quota usage
echo "=== Current vCPU Quota Usage ==="
az vm list-usage --location eastus --query "[?contains(name.value, 'cores')].{Name:name.localizedValue, Current:currentValue, Limit:limit}" -o table

echo
echo "=== Current AKS Configuration ==="
az aks show --resource-group "$AKS_RESOURCE_GROUP" --name "$AKS_CLUSTER_NAME" --query "agentPoolProfiles[].{Name:name, Count:count, VmSize:vmSize, Mode:mode}" -o table

echo
echo "Since we can't delete the only node pool, let's try creating a temporary second pool"
echo "and then switching to it. This requires careful quota management."
echo

# First, let's wait a bit more for the Windows VM quota to be fully freed
echo "Waiting for Windows VM quota to be fully freed..."
sleep 60

# Check quota again
echo
echo "=== Updated vCPU Quota Usage ==="
az vm list-usage --location eastus --query "[?contains(name.value, 'cores')].{Name:name.localizedValue, Current:currentValue, Limit:limit}" -o table

CURRENT_USAGE=$(az vm list-usage --location eastus --query "[?contains(name.value, 'cores')].currentValue" -o tsv)
echo "Current vCPU usage: $CURRENT_USAGE/4"

if [ "$CURRENT_USAGE" -gt 2 ]; then
    echo "⚠️  Still using too much quota. Let's try a different approach."
    echo
    echo "Alternative: Request quota increase"
    echo "1. Go to Azure Portal: https://portal.azure.com"
    echo "2. Search for 'Quotas'"
    echo "3. Select 'Compute' quotas"
    echo "4. Find 'Total Regional vCPUs' for East US"
    echo "5. Request increase to 8 vCPUs"
    echo
    echo "Or try again in a few minutes when quota is fully freed."
    exit 1
fi

echo
read -p "Do you want to proceed with creating a temporary second node pool? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Operation cancelled."
    exit 1
fi

echo
echo "🚀 Starting AKS node pool upgrade with temporary pool method..."

# Step 1: Create temporary node pool with larger VM size
echo
echo "Step 1: Creating temporary node pool with Standard_D4s_v3..."
az aks nodepool add \
    --resource-group "$AKS_RESOURCE_GROUP" \
    --cluster-name "$AKS_CLUSTER_NAME" \
    --name "temp-nodepool" \
    --node-count 1 \
    --node-vm-size "$NEW_VM_SIZE" \
    --mode System

if [ $? -ne 0 ]; then
    echo "❌ Failed to create temporary node pool!"
    echo "This might be due to quota limitations or the quota not being fully freed yet."
    echo "Please wait a few more minutes and try again, or request a quota increase."
    exit 1
fi

echo "✅ Temporary node pool created successfully!"

# Step 2: Wait for new node pool to be ready
echo
echo "Step 2: Waiting for temporary node pool to be ready..."
sleep 90

# Check if new node is ready
echo "Checking new node status..."
kubectl get nodes

# Step 3: Delete the old node pool
echo
echo "Step 3: Deleting old node pool..."
az aks nodepool delete \
    --resource-group "$AKS_RESOURCE_GROUP" \
    --cluster-name "$AKS_CLUSTER_NAME" \
    --name "$AKS_NODE_POOL_NAME"

if [ $? -ne 0 ]; then
    echo "❌ Failed to delete old node pool!"
    echo "You now have both node pools running. You may want to delete the old one manually."
    echo "Command: az aks nodepool delete --resource-group $AKS_RESOURCE_GROUP --cluster-name $AKS_CLUSTER_NAME --name $AKS_NODE_POOL_NAME"
else
    echo "✅ Old node pool deleted successfully!"
fi

# Step 4: Rename the temporary node pool (optional)
echo
echo "Step 4: The temporary node pool is now your main node pool."
echo "Note: The node pool is named 'temp-nodepool' instead of 'nodepool1'"

# Show final status
echo
echo "=== Final Configuration ==="
echo
echo "AKS Cluster Status:"
az aks show --resource-group "$AKS_RESOURCE_GROUP" --name "$AKS_CLUSTER_NAME" --query "agentPoolProfiles[].{Name:name, Count:count, VmSize:vmSize, Mode:mode}" -o table

echo
echo "Node Status:"
kubectl get nodes -o wide

echo
echo "Final vCPU Usage:"
az vm list-usage --location eastus --query "[?contains(name.value, 'cores')].{Name:name.localizedValue, Current:currentValue, Limit:limit}" -o table

echo
echo "Pod Status:"
kubectl get pods --all-namespaces | grep -v Running | head -5

echo
echo "🎉 SUCCESS! AKS node upgraded successfully!"
echo
echo "=== Upgrade Summary ==="
echo "✅ Deleted: Windows VM (freed quota)"
echo "✅ Upgraded: AKS node from Standard_D2s_v3 to Standard_D4s_v3"
echo "✅ CPU Performance: 2x improvement (2 → 4 vCPUs)"
echo "✅ Memory: 2x improvement (8 → 16 GB RAM)"
echo "✅ Node Pool: Now named 'temp-nodepool'"
echo
echo "=== Next Steps ==="
echo "1. Test your web services:"
echo "   curl -I https://nginx.seoedge.pro"
echo "   curl -I https://jenkins.seoedge.pro"
echo "   curl -I https://apach2.seoedge.pro"
echo "2. Monitor performance: kubectl top nodes"
echo "3. Check resource usage: kubectl top pods --all-namespaces"
