# Manual VM Creation Guide

## Azure CLI Commands (Run these one by one)

```bash
# 1. Set variables
VM_NAME="ubuntu-test-vm"
RESOURCE_GROUP="myResourceGroup"
LOCATION="eastus"
ADMIN_USERNAME="azureuser"

# 2. Generate SSH key (if not exists)
ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa -N ""

# 3. Create NSG
az network nsg create \
    --resource-group "$RESOURCE_GROUP" \
    --name "${VM_NAME}-nsg" \
    --location "$LOCATION"

# 4. Add SSH rule
az network nsg rule create \
    --resource-group "$RESOURCE_GROUP" \
    --nsg-name "${VM_NAME}-nsg" \
    --name "SSH" \
    --protocol tcp \
    --priority 1001 \
    --destination-port-range 22 \
    --access allow

# 5. Create Public IP
az network public-ip create \
    --resource-group "$RESOURCE_GROUP" \
    --name "${VM_NAME}-ip" \
    --location "$LOCATION"

# 6. Create NIC
az network nic create \
    --resource-group "$RESOURCE_GROUP" \
    --name "${VM_NAME}-nic" \
    --location "$LOCATION" \
    --subnet "/subscriptions/$(az account show --query id -o tsv)/resourceGroups/MC_myResourceGroup_myAKSCluster_eastus/providers/Microsoft.Network/virtualNetworks/aks-vnet-********/subnets/vm-subnet" \
    --network-security-group "${VM_NAME}-nsg" \
    --public-ip-address "${VM_NAME}-ip"

# 7. Create VM
az vm create \
    --resource-group "$RESOURCE_GROUP" \
    --name "$VM_NAME" \
    --location "$LOCATION" \
    --size "Standard_B2s" \
    --image "Ubuntu2204" \
    --admin-username "$ADMIN_USERNAME" \
    --ssh-key-values ~/.ssh/id_rsa.pub \
    --nics "${VM_NAME}-nic"

# 8. Get connection info
az vm show -d --resource-group "$RESOURCE_GROUP" --name "$VM_NAME" --query "{Name:name, PublicIP:publicIps, PrivateIP:privateIps}" -o table
```

## After VM Creation

1. **Connect to VM**:
   ```bash
   ssh azureuser@<PUBLIC_IP>
   ```

2. **Copy setup script**:
   ```bash
   scp setup-ubuntu-vm.sh azureuser@<PUBLIC_IP>:~/
   ```

3. **Run setup**:
   ```bash
   ssh azureuser@<PUBLIC_IP>
   ./setup-ubuntu-vm.sh
   ```

4. **Configure kubectl**:
   ```bash
   az aks get-credentials --resource-group myResourceGroup --name myAKSCluster
   ```

## Testing Your Services

Once setup is complete, you can test your AKS services:

```bash
# Test external services
curl -I https://nginx.seoedge.pro
curl -I https://jenkins.seoedge.pro
curl -I https://apach2.seoedge.pro

# Test internal services
kubectl get services --all-namespaces
kubectl port-forward service/jenkins 8080:8080 &
curl http://localhost:8080
```

## Network Architecture

Your setup will look like this:

```
Azure Virtual Network (aks-vnet-********)
├── AKS Subnet (**********/16)
│   ├── AKS Node Pool
│   └── Kubernetes Services
└── VM Subnet (**********/24)
    └── Ubuntu Test VM
        ├── Public IP (for external access)
        └── Private IP (for internal communication)
```

The VM will be able to:
- Access your AKS services directly via internal IPs
- Test your ingress endpoints
- Deploy and test applications in the same network
- Use kubectl to manage your cluster
