# Ubuntu 24.04 Server VM for AKS Testing

This repository contains scripts to create and configure an Ubuntu 24.04 Server virtual machine on Azure that shares the same internal network as your AKS cluster. This setup allows you to test web services and deploy applications that can communicate directly with your Kubernetes resources.

## 🎯 Purpose

- **Testing**: Test web services running in your AKS cluster from a dedicated VM
- **Development**: Deploy and test applications in the same network environment
- **Debugging**: Access internal cluster services for troubleshooting
- **CI/CD**: Use as a build/deployment agent with direct cluster access

## 📋 Prerequisites

- Azure CLI installed and configured (`az login`)
- An existing AKS cluster in Azure
- SSH key pair (will be generated if not present)
- Appropriate Azure permissions to create VMs and network resources

## 🚀 Quick Start

### Step 1: Gather AKS Network Information

```bash
# Make the script executable
chmod +x get-aks-network-info.sh

# Run the network discovery script
./get-aks-network-info.sh
```

This script will:
- List your AKS clusters
- Extract virtual network and subnet information
- Save configuration to `aks-network-config.env`

### Step 2: Create the Ubuntu VM

```bash
# Make the script executable
chmod +x create-ubuntu-vm.sh

# Create the VM in the same network as your AKS cluster
./create-ubuntu-vm.sh
```

This script will:
- Create a new Ubuntu 24.04 Server VM
- Place it in the same virtual network as your AKS cluster
- Configure network security groups with appropriate rules
- Create a public IP for external access
- Save connection information to `vm-connection-info.txt`

### Step 3: Connect and Setup the VM

```bash
# Connect to your new VM (use the IP from vm-connection-info.txt)
ssh azureuser@<VM_PUBLIC_IP>

# Download and run the setup script
curl -O https://raw.githubusercontent.com/your-repo/setup-ubuntu-vm.sh
chmod +x setup-ubuntu-vm.sh
./setup-ubuntu-vm.sh
```

Or copy the setup script to the VM:
```bash
scp setup-ubuntu-vm.sh azureuser@<VM_PUBLIC_IP>:~/
ssh azureuser@<VM_PUBLIC_IP>
./setup-ubuntu-vm.sh
```

## 🛠️ What Gets Installed

The setup script installs and configures:

### Development Tools
- **Docker & Docker Compose** - Container runtime and orchestration
- **Node.js & npm** - JavaScript runtime and package manager
- **Python 3 & pip** - Python runtime and package manager
- **Go** - Go programming language
- **Git** - Version control system

### Azure & Kubernetes Tools
- **Azure CLI** - Azure command-line interface
- **kubectl** - Kubernetes command-line tool
- **Helm** - Kubernetes package manager

### Network Testing Tools
- **curl & wget** - HTTP clients
- **httpie** - User-friendly HTTP client
- **siege** - HTTP load testing tool
- **apache2-utils** - Apache benchmarking tools
- **nmap** - Network discovery and security auditing

### Custom Scripts
- **test-web-service.sh** - Test any web service endpoint
- **test-aks-services.sh** - Test your specific AKS services
- **simple-http-server.py** - Simple HTTP server for testing

## 🔧 Configuration

### Network Configuration

The VM is configured with the following network access:
- **SSH (22)** - Remote access
- **HTTP (80)** - Web services
- **HTTPS (443)** - Secure web services
- **Custom ports (8080, 3000, 5000, 8000, 9000)** - Development services

### AKS Integration

After setup, configure kubectl to connect to your AKS cluster:

```bash
# Login to Azure
az login

# Get AKS credentials
az aks get-credentials --resource-group <your-resource-group> --name <your-cluster-name>

# Test connection
kubectl get nodes
kubectl get services --all-namespaces
```

## 🧪 Testing Your Services

### Test External Services
```bash
# Test your ingress endpoints
test-web https://nginx.seoedge.pro
test-web https://jenkins.seoedge.pro
test-web https://apach2.seoedge.pro
```

### Test Internal Services
```bash
# Run comprehensive AKS service tests
test-aks

# Test specific internal service
kubectl port-forward service/jenkins 8080:8080 &
test-web http://localhost:8080
```

### Start Test Server
```bash
# Start a simple HTTP server for testing
http-server 8000

# Test from another terminal
curl http://localhost:8000/health
curl http://localhost:8000/info
```

## 📁 File Structure

```
.
├── get-aks-network-info.sh     # Discovers AKS network configuration
├── create-ubuntu-vm.sh         # Creates the Ubuntu VM
├── setup-ubuntu-vm.sh          # Configures the VM with tools
├── README-Ubuntu-VM-Setup.md   # This documentation
├── aks-network-config.env      # Generated network configuration
└── vm-connection-info.txt      # Generated VM connection details
```

## 🔍 Useful Commands

After setup, you'll have these aliases available:

```bash
# Kubernetes shortcuts
k get pods                    # kubectl get pods
kgp                          # kubectl get pods
kgs                          # kubectl get services
kgn                          # kubectl get nodes

# Docker shortcuts
dps                          # docker ps
dimg                         # docker images

# Testing shortcuts
test-web <url>               # Test web service
test-aks                     # Test AKS services
http-server [port]           # Start HTTP server

# Utility functions
kexec <pod-name>             # Execute into pod
klogs <pod-name>             # Follow pod logs
```

## 🌐 Network Architecture

```
Azure Virtual Network
├── AKS Subnet (********/24)
│   ├── AKS Node Pool
│   └── Kubernetes Services
└── VM Subnet (********/24)
    └── Ubuntu Test VM
        ├── Public IP (for external access)
        └── Private IP (for internal communication)
```

## 🔒 Security Considerations

- The VM has a public IP for management access
- Network Security Group restricts access to necessary ports
- SSH key authentication is used (no password)
- Consider using Azure Bastion for enhanced security in production

## 🚨 Troubleshooting

### Common Issues

1. **Cannot connect to AKS cluster**
   ```bash
   az aks get-credentials --resource-group <rg> --name <cluster> --overwrite-existing
   ```

2. **Docker permission denied**
   ```bash
   sudo usermod -aG docker $USER
   newgrp docker
   ```

3. **Network connectivity issues**
   ```bash
   # Check VM network configuration
   ip addr show
   ip route show
   
   # Test DNS resolution
   nslookup nginx.seoedge.pro
   ```

4. **Service not accessible**
   ```bash
   # Check if service is running in cluster
   kubectl get services --all-namespaces
   kubectl get ingress --all-namespaces
   
   # Test from within cluster
   kubectl run test-pod --image=busybox -it --rm -- wget -qO- http://service-name
   ```

## 🧹 Cleanup

To remove the created resources:

```bash
# Delete the VM and associated resources
az vm delete --resource-group <resource-group> --name <vm-name>
az disk delete --resource-group <resource-group> --name <vm-name>_OsDisk_*
az network nic delete --resource-group <resource-group> --name <vm-name>-nic
az network public-ip delete --resource-group <resource-group> --name <vm-name>-ip
az network nsg delete --resource-group <resource-group> --name <vm-name>-nsg
```

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify your Azure permissions
3. Ensure your AKS cluster is running and accessible
4. Check Azure portal for any resource creation errors

---

**Happy testing! 🎉**
