#!/bin/bash

echo "=== Delete Windows VM and Upgrade AKS Node ==="

# Variables
WINDOWS_VM_NAME="test-environment"
WINDOWS_RESOURCE_GROUP="TEST-ENVIRONMEN"
AKS_CLUSTER_NAME="myAKSCluster"
AKS_RESOURCE_GROUP="myResourceGroup"
AKS_NODE_POOL_NAME="nodepool1"
NEW_VM_SIZE="Standard_D4s_v3"

echo "Windows VM: $WINDOWS_VM_NAME"
echo "Windows Resource Group: $WINDOWS_RESOURCE_GROUP"
echo "AKS Cluster: $AKS_CLUSTER_NAME"
echo "AKS Resource Group: $AKS_RESOURCE_GROUP"
echo

# Show current quota usage
echo "=== Current vCPU Quota Usage ==="
az vm list-usage --location eastus --query "[?contains(name.value, 'cores')].{Name:name.localizedValue, Current:currentValue, Limit:limit}" -o table

echo
echo "=== Current VMs ==="
az vm list --query "[].{Name:name, ResourceGroup:resourceGroup, VmSize:hardwareProfile.vmSize, OS:storageProfile.osDisk.osType}" -o table

echo
echo "=== Plan ==="
echo "1. Delete Windows VM '$WINDOWS_VM_NAME' (frees ~2 vCPUs)"
echo "2. Upgrade AKS node from Standard_D2s_v3 (2 vCPUs) to Standard_D4s_v3 (4 vCPUs)"
echo "3. Net result: Same total vCPU usage, but better AKS performance"
echo

read -p "Do you want to proceed with deleting the Windows VM and upgrading AKS? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Operation cancelled."
    exit 1
fi

echo
echo "🚀 Starting the process..."

# Step 1: Delete the Windows VM and its resources
echo
echo "Step 1: Deleting Windows VM and associated resources..."

# Get VM details before deletion
echo "Getting VM network interface and disk information..."
VM_NIC=$(az vm show --resource-group "$WINDOWS_RESOURCE_GROUP" --name "$WINDOWS_VM_NAME" --query "networkProfile.networkInterfaces[0].id" -o tsv 2>/dev/null)
VM_DISK=$(az vm show --resource-group "$WINDOWS_RESOURCE_GROUP" --name "$WINDOWS_VM_NAME" --query "storageProfile.osDisk.name" -o tsv 2>/dev/null)

# Delete the VM
echo "Deleting VM: $WINDOWS_VM_NAME..."
az vm delete --resource-group "$WINDOWS_RESOURCE_GROUP" --name "$WINDOWS_VM_NAME" --yes

if [ $? -ne 0 ]; then
    echo "❌ Failed to delete Windows VM!"
    exit 1
fi

echo "✅ Windows VM deleted successfully!"

# Clean up associated resources
if [ -n "$VM_NIC" ]; then
    NIC_NAME=$(echo "$VM_NIC" | cut -d'/' -f9)
    echo "Deleting network interface: $NIC_NAME..."
    az network nic delete --resource-group "$WINDOWS_RESOURCE_GROUP" --name "$NIC_NAME" 2>/dev/null
fi

if [ -n "$VM_DISK" ]; then
    echo "Deleting OS disk: $VM_DISK..."
    az disk delete --resource-group "$WINDOWS_RESOURCE_GROUP" --name "$VM_DISK" --yes 2>/dev/null
fi

# Wait for resources to be freed
echo "Waiting for vCPU quota to be freed..."
sleep 30

# Check quota after deletion
echo
echo "=== vCPU Quota After VM Deletion ==="
az vm list-usage --location eastus --query "[?contains(name.value, 'cores')].{Name:name.localizedValue, Current:currentValue, Limit:limit}" -o table

# Step 2: Upgrade AKS node pool
echo
echo "Step 2: Upgrading AKS node pool..."

# Create new node pool with larger VM size
echo "Creating new node pool with Standard_D4s_v3 (4 vCPUs)..."
az aks nodepool add \
    --resource-group "$AKS_RESOURCE_GROUP" \
    --cluster-name "$AKS_CLUSTER_NAME" \
    --name "nodepool2" \
    --node-count 1 \
    --node-vm-size "$NEW_VM_SIZE" \
    --mode System

if [ $? -ne 0 ]; then
    echo "❌ Failed to create new node pool!"
    echo "The Windows VM has been deleted, but AKS upgrade failed."
    echo "You may need to wait longer for quota to be freed or try again."
    exit 1
fi

echo "✅ New node pool created successfully!"

# Wait for new node pool to be ready
echo "Waiting for new node pool to be ready..."
sleep 60

# Delete old node pool
echo "Deleting old node pool..."
az aks nodepool delete \
    --resource-group "$AKS_RESOURCE_GROUP" \
    --cluster-name "$AKS_CLUSTER_NAME" \
    --name "$AKS_NODE_POOL_NAME" \
    --yes \
    --no-wait

if [ $? -ne 0 ]; then
    echo "⚠️  Warning: Failed to delete old node pool. You may need to delete it manually."
else
    echo "✅ Old node pool deletion initiated!"
fi

# Wait for changes to propagate
echo
echo "Step 3: Waiting for changes to propagate..."
sleep 30

# Show final status
echo
echo "=== Final Configuration ==="
echo
echo "AKS Cluster Status:"
az aks show --resource-group "$AKS_RESOURCE_GROUP" --name "$AKS_CLUSTER_NAME" --query "agentPoolProfiles[].{Name:name, Count:count, VmSize:vmSize, Mode:mode}" -o table

echo
echo "Node Status:"
kubectl get nodes -o wide

echo
echo "Final vCPU Usage:"
az vm list-usage --location eastus --query "[?contains(name.value, 'cores')].{Name:name.localizedValue, Current:currentValue, Limit:limit}" -o table

echo
echo "🎉 SUCCESS! Windows VM deleted and AKS node upgraded!"
echo
echo "=== Summary ==="
echo "✅ Deleted: Windows VM '$WINDOWS_VM_NAME' (freed ~2 vCPUs)"
echo "✅ Upgraded: AKS node from Standard_D2s_v3 to Standard_D4s_v3"
echo "✅ Result: 2x CPU performance for AKS workloads"
echo "✅ Quota: Efficiently used existing vCPU allocation"
echo
echo "=== Next Steps ==="
echo "1. Test your web services:"
echo "   curl -I https://nginx.seoedge.pro"
echo "   curl -I https://jenkins.seoedge.pro"
echo "   curl -I https://apach2.seoedge.pro"
echo "2. Monitor AKS performance: kubectl top nodes"
echo "3. Check pod resource usage: kubectl top pods --all-namespaces"
