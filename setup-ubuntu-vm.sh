#!/bin/bash

# Setup script for Ubuntu 24.04 Server VM
# This script installs essential tools for testing web services and deployment

echo "=== Ubuntu 24.04 Server Setup for AKS Testing ==="
echo "This script will install essential tools for web service testing and deployment"
echo

# Update system
echo "📦 Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install essential packages
echo "📦 Installing essential packages..."
sudo apt install -y \
    curl \
    wget \
    git \
    vim \
    nano \
    htop \
    tree \
    jq \
    unzip \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    net-tools \
    telnet \
    nmap \
    dnsutils

# Install Docker
echo "🐳 Installing Docker..."
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# Add user to docker group
sudo usermod -aG docker $USER

# Install Docker Compose (standalone)
echo "🐳 Installing Docker Compose..."
DOCKER_COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | jq -r .tag_name)
sudo curl -L "https://github.com/docker/compose/releases/download/${DOCKER_COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install Azure CLI
echo "☁️ Installing Azure CLI..."
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash

# Install kubectl
echo "⚙️ Installing kubectl..."
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
rm kubectl

# Install Helm
echo "⚙️ Installing Helm..."
curl https://baltocdn.com/helm/signing.asc | gpg --dearmor | sudo tee /usr/share/keyrings/helm.gpg > /dev/null
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/helm.gpg] https://baltocdn.com/helm/stable/debian/ all main" | sudo tee /etc/apt/sources.list.d/helm-stable-debian.list
sudo apt update
sudo apt install -y helm

# Install Node.js and npm
echo "📦 Installing Node.js and npm..."
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt install -y nodejs

# Install Python and pip
echo "🐍 Installing Python and pip..."
sudo apt install -y python3 python3-pip python3-venv

# Install useful Python packages
pip3 install --user requests httpie

# Install Go
echo "🔧 Installing Go..."
GO_VERSION=$(curl -s https://api.github.com/repos/golang/go/releases/latest | jq -r .tag_name)
wget "https://golang.org/dl/${GO_VERSION}.linux-amd64.tar.gz"
sudo rm -rf /usr/local/go && sudo tar -C /usr/local -xzf "${GO_VERSION}.linux-amd64.tar.gz"
rm "${GO_VERSION}.linux-amd64.tar.gz"

# Add Go to PATH
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
echo 'export PATH=$PATH:$HOME/go/bin' >> ~/.bashrc

# Install useful network testing tools
echo "🌐 Installing network testing tools..."
sudo apt install -y \
    httpie \
    siege \
    apache2-utils \
    iperf3

# Create useful directories
echo "📁 Creating useful directories..."
mkdir -p ~/projects
mkdir -p ~/scripts
mkdir -p ~/logs

# Create a simple web server test script
echo "📝 Creating test scripts..."
cat > ~/scripts/test-web-service.sh << 'EOF'
#!/bin/bash
# Simple script to test web services

if [ $# -eq 0 ]; then
    echo "Usage: $0 <url> [method] [data]"
    echo "Examples:"
    echo "  $0 http://nginx.seoedge.pro"
    echo "  $0 http://jenkins.seoedge.pro"
    echo "  $0 http://**********:8080 GET"
    echo "  $0 http://api.example.com POST '{\"key\":\"value\"}'"
    exit 1
fi

URL=$1
METHOD=${2:-GET}
DATA=$3

echo "Testing web service: $URL"
echo "Method: $METHOD"
echo "=========================="

if [ -n "$DATA" ]; then
    curl -X "$METHOD" -H "Content-Type: application/json" -d "$DATA" -v "$URL"
else
    curl -X "$METHOD" -v "$URL"
fi

echo
echo "=========================="
echo "Response headers and timing:"
curl -X "$METHOD" -H "Content-Type: application/json" -w "\nHTTP Status: %{http_code}\nTotal Time: %{time_total}s\nConnect Time: %{time_connect}s\nSize: %{size_download} bytes\n" -s -o /dev/null "$URL"
EOF

chmod +x ~/scripts/test-web-service.sh

# Create a script to test AKS services
cat > ~/scripts/test-aks-services.sh << 'EOF'
#!/bin/bash
# Script to test AKS services from the VM

echo "=== Testing AKS Services from VM ==="
echo

# Test services based on your ingress configuration
SERVICES=(
    "https://nginx.seoedge.pro"
    "https://apach2.seoedge.pro"
    "https://jenkins.seoedge.pro"
)

for service in "${SERVICES[@]}"; do
    echo "Testing: $service"
    echo "------------------------"
    
    # Test HTTP response
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$service")
    RESPONSE_TIME=$(curl -s -o /dev/null -w "%{time_total}" "$service")
    
    if [ "$HTTP_STATUS" -eq 200 ] || [ "$HTTP_STATUS" -eq 301 ] || [ "$HTTP_STATUS" -eq 302 ]; then
        echo "✅ $service - Status: $HTTP_STATUS, Time: ${RESPONSE_TIME}s"
    else
        echo "❌ $service - Status: $HTTP_STATUS, Time: ${RESPONSE_TIME}s"
    fi
    
    echo
done

echo "=== Testing Internal Cluster Communication ==="
echo "Note: You'll need to configure kubectl to connect to your AKS cluster"
echo "Run: az aks get-credentials --resource-group <rg> --name <cluster-name>"
echo

# Test if kubectl is configured
if kubectl cluster-info &> /dev/null; then
    echo "✅ kubectl is configured and can connect to cluster"
    
    echo "Cluster services:"
    kubectl get services --all-namespaces
    
    echo
    echo "Cluster nodes:"
    kubectl get nodes
else
    echo "⚠️  kubectl is not configured. Run the following to configure:"
    echo "az aks get-credentials --resource-group <your-rg> --name <your-cluster>"
fi
EOF

chmod +x ~/scripts/test-aks-services.sh

# Create a simple HTTP server script for testing
cat > ~/scripts/simple-http-server.py << 'EOF'
#!/usr/bin/env python3
"""
Simple HTTP server for testing
Usage: python3 simple-http-server.py [port]
"""

import http.server
import socketserver
import sys
import json
from datetime import datetime

class TestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'server': 'Ubuntu Test VM'
            }
            self.wfile.write(json.dumps(response).encode())
        elif self.path == '/info':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                'server': 'Ubuntu Test VM',
                'timestamp': datetime.now().isoformat(),
                'client_ip': self.client_address[0],
                'user_agent': self.headers.get('User-Agent', 'Unknown')
            }
            self.wfile.write(json.dumps(response).encode())
        else:
            super().do_GET()

if __name__ == "__main__":
    PORT = int(sys.argv[1]) if len(sys.argv) > 1 else 8000
    
    with socketserver.TCPServer(("", PORT), TestHandler) as httpd:
        print(f"🌐 Test HTTP server running on port {PORT}")
        print(f"   Health check: http://localhost:{PORT}/health")
        print(f"   Info endpoint: http://localhost:{PORT}/info")
        print("   Press Ctrl+C to stop")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server stopped")
EOF

chmod +x ~/scripts/simple-http-server.py

# Set up bash aliases
echo "⚙️ Setting up useful aliases..."
cat >> ~/.bashrc << 'EOF'

# Custom aliases for AKS testing
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'
alias k='kubectl'
alias kgp='kubectl get pods'
alias kgs='kubectl get services'
alias kgn='kubectl get nodes'
alias dps='docker ps'
alias dimg='docker images'
alias test-web='~/scripts/test-web-service.sh'
alias test-aks='~/scripts/test-aks-services.sh'
alias http-server='python3 ~/scripts/simple-http-server.py'

# Useful functions
function kexec() {
    kubectl exec -it $1 -- /bin/bash
}

function klogs() {
    kubectl logs -f $1
}
EOF

echo
echo "✅ Setup completed successfully!"
echo
echo "=== What's been installed ==="
echo "• Docker & Docker Compose"
echo "• Azure CLI"
echo "• kubectl (Kubernetes CLI)"
echo "• Helm"
echo "• Node.js & npm"
echo "• Python 3 & pip"
echo "• Go programming language"
echo "• Network testing tools (curl, httpie, siege, etc.)"
echo "• Custom testing scripts in ~/scripts/"
echo
echo "=== Next Steps ==="
echo "1. Logout and login again (or run 'newgrp docker') to use Docker without sudo"
echo "2. Configure kubectl to connect to your AKS cluster:"
echo "   az login"
echo "   az aks get-credentials --resource-group <your-rg> --name <your-cluster>"
echo "3. Test your services:"
echo "   ~/scripts/test-aks-services.sh"
echo "4. Start a simple test server:"
echo "   python3 ~/scripts/simple-http-server.py 8000"
echo
echo "=== Useful Commands ==="
echo "• test-web <url>           - Test any web service"
echo "• test-aks                 - Test your AKS services"
echo "• http-server [port]       - Start simple HTTP server"
echo "• k get pods               - List Kubernetes pods"
echo "• docker ps                - List running containers"
echo
echo "🎉 Your Ubuntu VM is ready for AKS testing and deployment!"
