#!/bin/bash

echo "=== Optimize Current AKS Setup (No VM Size Change) ==="

# Variables
AKS_CLUSTER_NAME="myAKSCluster"
AKS_RESOURCE_GROUP="myResourceGroup"

echo "Since we're at quota limit, let's optimize your current setup:"
echo "1. Enable cluster autoscaler for better resource management"
echo "2. Set resource limits on pods"
echo "3. Check for resource optimization opportunities"
echo

# Show current resource usage
echo "=== Current Resource Usage ==="
kubectl top nodes 2>/dev/null || echo "Metrics server not ready yet"
echo

kubectl top pods --all-namespaces 2>/dev/null | head -10 || echo "Pod metrics not ready yet"

echo
echo "=== Current Node Capacity ==="
kubectl describe nodes | grep -A 5 "Capacity:\|Allocatable:"

echo
echo "=== Optimization Options ==="
echo "1. Enable cluster autoscaler (scale 1-3 nodes as needed)"
echo "2. Add resource limits to your deployments"
echo "3. Enable horizontal pod autoscaler"
echo

read -p "Do you want to enable cluster autoscaler? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Enabling cluster autoscaler..."
    az aks nodepool update \
        --resource-group "$AKS_RESOURCE_GROUP" \
        --cluster-name "$AKS_CLUSTER_NAME" \
        --name nodepool1 \
        --enable-cluster-autoscaler \
        --min-count 1 \
        --max-count 2
    
    if [ $? -eq 0 ]; then
        echo "✅ Cluster autoscaler enabled! (1-2 nodes)"
        echo "⚠️  Note: This will create a second node when needed, using your quota."
    else
        echo "❌ Failed to enable cluster autoscaler"
    fi
fi

echo
echo "=== Resource Optimization Tips ==="
echo "1. Add resource requests/limits to your deployments:"
echo "   resources:"
echo "     requests:"
echo "       memory: \"64Mi\""
echo "       cpu: \"250m\""
echo "     limits:"
echo "       memory: \"128Mi\""
echo "       cpu: \"500m\""
echo
echo "2. Use horizontal pod autoscaler:"
echo "   kubectl autoscale deployment <deployment-name> --cpu-percent=50 --min=1 --max=3"
echo
echo "3. Monitor resource usage:"
echo "   kubectl top nodes"
echo "   kubectl top pods --all-namespaces"

echo
echo "=== Current AKS Configuration ==="
az aks show --resource-group "$AKS_RESOURCE_GROUP" --name "$AKS_CLUSTER_NAME" --query "agentPoolProfiles[].{Name:name, Count:count, VmSize:vmSize, AutoScaling:enableAutoScaling, MinCount:minCount, MaxCount:maxCount}" -o table

echo
echo "🎯 Summary:"
echo "✅ Your current setup can handle moderate workloads"
echo "💡 For significant performance improvement, request quota increase"
echo "📈 Monitor usage with: kubectl top nodes"
echo "🔄 Autoscaler will add nodes when CPU/memory usage is high"
