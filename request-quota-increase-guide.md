# Azure vCPU Quota Increase Guide

## Current Situation
- **Current Quota**: 4 vCPUs in East US region
- **Current Usage**: 4/4 vCPUs (100% used)
- **Needed**: 6-8 vCPUs to upgrade AKS node

## How to Request Quota Increase

### Method 1: Azure Portal (Recommended)
1. **Go to Azure Portal**: https://portal.azure.com
2. **Search for "Quotas"** in the top search bar
3. **Select "My quotas"**
4. **Filter by**:
   - Service: `Compute`
   - Region: `East US`
5. **Find "Total Regional vCPUs"**
6. **Click "Request increase"**
7. **Request new limit**: `8 vCPUs` (or more if you plan to expand)
8. **Provide justification**: 
   ```
   Need to upgrade AKS cluster node from Standard_D2s_v3 to Standard_D4s_v3 
   for improved application performance. Currently at quota limit with 
   ubuntu-jump VM (2 vCPUs) + AKS node (2 vCPUs) = 4/4 vCPUs used.
   ```

### Method 2: Azure CLI
```bash
# Create support request for quota increase
az support tickets create \
    --ticket-name "vCPU Quota Increase Request" \
    --description "Need to increase vCPU quota from 4 to 8 in East US region for AKS upgrade" \
    --severity "minimal" \
    --problem-classification "/providers/Microsoft.Support/services/06bfd9d3-516b-d5c6-5802-169c800dec89/problemClassifications/13491426-645c-9b21-9a78-469a3aa2be52"
```

## Timeline
- **Processing Time**: Usually 1-2 business days
- **Approval**: Almost always approved for reasonable increases
- **Cost**: No additional cost for quota increase

## After Quota Increase
Once approved, run this command to upgrade your AKS node:

```bash
# Create new node pool with 4 vCPUs
az aks nodepool add \
    --resource-group myResourceGroup \
    --cluster-name myAKSCluster \
    --name nodepool2 \
    --node-count 1 \
    --node-vm-size Standard_D4s_v3 \
    --mode System

# Delete old node pool
az aks nodepool delete \
    --resource-group myResourceGroup \
    --cluster-name myAKSCluster \
    --name nodepool1
```

## Alternative: Temporary Workaround
If you need immediate improvement, consider:
1. **Optimize current resources** with resource limits
2. **Use node auto-scaling** to handle load spikes
3. **Upgrade to newer VM series** with same vCPU count but better performance

## Contact Information
- **Azure Support**: Available 24/7 through Azure Portal
- **Documentation**: https://docs.microsoft.com/en-us/azure/quotas/
