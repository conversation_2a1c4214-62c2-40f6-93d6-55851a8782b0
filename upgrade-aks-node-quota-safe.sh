#!/bin/bash

echo "=== Upgrading AKS Node CPU Size (Quota-Safe Method) ==="

# Variables
CLUSTER_NAME="myAKSCluster"
RESOURCE_GROUP="myResourceGroup"
NODE_POOL_NAME="nodepool1"
CURRENT_VM_SIZE="Standard_D2s_v3"
NEW_VM_SIZE="Standard_D4s_v3"

echo "Cluster: $CLUSTER_NAME"
echo "Resource Group: $RESOURCE_GROUP"
echo "Node Pool: $NODE_POOL_NAME"
echo "Current VM Size: $CURRENT_VM_SIZE (2 vCPUs, 8 GB RAM)"
echo "New VM Size: $NEW_VM_SIZE (4 vCPUs, 16 GB RAM)"
echo

# Check current quota usage
echo "=== Current vCPU Quota Usage ==="
az vm list-usage --location eastus --query "[?contains(name.value, 'cores')].{Name:name.localizedValue, Current:currentValue, Limit:limit}" -o table

echo
echo "⚠️  QUOTA LIMITATION DETECTED!"
echo "Your current vCPU quota is 4 cores, and you're using all 4."
echo "To upgrade the AKS node, we need to use a zero-downtime approach."
echo

# Show current configuration
echo "=== Current Configuration ==="
az aks show --resource-group "$RESOURCE_GROUP" --name "$CLUSTER_NAME" --query "agentPoolProfiles[].{Name:name, Count:count, VmSize:vmSize, Mode:mode}" -o table

echo
echo "=== Upgrade Options ==="
echo "Option 1: Request quota increase (Recommended)"
echo "Option 2: Delete and recreate node pool (Brief downtime)"
echo "Option 3: Use smaller VM size upgrade"
echo

read -p "Which option would you like to choose? (1/2/3): " -n 1 -r
echo

case $REPLY in
    1)
        echo
        echo "=== Option 1: Request Quota Increase ==="
        echo "To increase your vCPU quota:"
        echo "1. Go to Azure Portal: https://portal.azure.com"
        echo "2. Search for 'Quotas'"
        echo "3. Select 'Compute' quotas"
        echo "4. Find 'Total Regional vCPUs' for East US"
        echo "5. Request increase to at least 8 vCPUs"
        echo
        echo "After quota increase, run this command:"
        echo "az aks nodepool add --resource-group $RESOURCE_GROUP --cluster-name $CLUSTER_NAME --name nodepool2 --node-count 1 --node-vm-size $NEW_VM_SIZE --mode System"
        echo "az aks nodepool delete --resource-group $RESOURCE_GROUP --cluster-name $CLUSTER_NAME --name $NODE_POOL_NAME --yes"
        echo
        echo "📋 Quota increase usually takes 1-2 business days to process."
        exit 0
        ;;
    2)
        echo
        echo "=== Option 2: Delete and Recreate (Brief Downtime) ==="
        echo "⚠️  This will cause brief service interruption!"
        read -p "Are you sure you want to proceed? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "❌ Operation cancelled."
            exit 1
        fi
        
        echo "🚀 Starting delete and recreate process..."
        
        # Delete the current node pool
        echo "Step 1: Deleting current node pool..."
        az aks nodepool delete \
            --resource-group "$RESOURCE_GROUP" \
            --cluster-name "$CLUSTER_NAME" \
            --name "$NODE_POOL_NAME" \
            --yes
        
        if [ $? -ne 0 ]; then
            echo "❌ Failed to delete node pool!"
            exit 1
        fi
        
        echo "✅ Node pool deleted!"
        
        # Wait a moment
        echo "Waiting for resources to be freed..."
        sleep 30
        
        # Create new node pool with larger VM size
        echo "Step 2: Creating new node pool with larger VM size..."
        az aks nodepool add \
            --resource-group "$RESOURCE_GROUP" \
            --cluster-name "$CLUSTER_NAME" \
            --name "$NODE_POOL_NAME" \
            --node-count 1 \
            --node-vm-size "$NEW_VM_SIZE" \
            --mode System
        
        if [ $? -ne 0 ]; then
            echo "❌ Failed to create new node pool!"
            echo "⚠️  Your cluster may be in an unstable state. Please check Azure Portal."
            exit 1
        fi
        
        echo "✅ New node pool created successfully!"
        ;;
    3)
        echo
        echo "=== Option 3: Smaller VM Size Upgrade ==="
        echo "Available options that fit within your quota:"
        echo "- Keep Standard_D2s_v3 (2 vCPUs) - No change"
        echo "- No other options available with current quota"
        echo
        echo "💡 Recommendation: Choose Option 1 (Quota increase) for best results."
        exit 0
        ;;
    *)
        echo "❌ Invalid option selected."
        exit 1
        ;;
esac

# Wait for changes to propagate
echo
echo "Step 3: Waiting for changes to propagate..."
sleep 60

# Show final configuration
echo
echo "=== Final Configuration ==="
az aks show --resource-group "$RESOURCE_GROUP" --name "$CLUSTER_NAME" --query "agentPoolProfiles[].{Name:name, Count:count, VmSize:vmSize, Mode:mode}" -o table

echo
echo "=== Node Status ==="
kubectl get nodes -o wide

echo
echo "=== Pod Status ==="
kubectl get pods --all-namespaces --field-selector=status.phase!=Running | head -10

echo
echo "🎉 SUCCESS! AKS node CPU upgrade completed!"
echo
echo "=== Upgrade Summary ==="
echo "✅ Old VM Size: $CURRENT_VM_SIZE (2 vCPUs, 8 GB RAM)"
echo "✅ New VM Size: $NEW_VM_SIZE (4 vCPUs, 16 GB RAM)"
echo "✅ CPU Performance: 2x improvement"
echo "✅ Memory: 2x improvement"
echo
echo "=== Cost Impact ==="
echo "⚠️  Note: The new VM size will cost approximately 2x more than the previous size."
echo
echo "=== Next Steps ==="
echo "1. Test your web services:"
echo "   curl -I https://nginx.seoedge.pro"
echo "   curl -I https://jenkins.seoedge.pro"
echo "   curl -I https://apach2.seoedge.pro"
echo "2. Monitor resource usage: kubectl top nodes"
echo "3. Check application performance"
