#!/bin/bash

echo "=== Upgrade AKS Node (Delete-First Method) ==="

# Variables
AKS_CLUSTER_NAME="myAKSCluster"
AKS_RESOURCE_GROUP="myResourceGroup"
AKS_NODE_POOL_NAME="nodepool1"
NEW_VM_SIZE="Standard_D4s_v3"

echo "AKS Cluster: $AKS_CLUSTER_NAME"
echo "AKS Resource Group: $AKS_RESOURCE_GROUP"
echo "Current Node Pool: $AKS_NODE_POOL_NAME"
echo "New VM Size: $NEW_VM_SIZE (4 vCPUs, 16 GB RAM)"
echo

# Show current quota usage
echo "=== Current vCPU Quota Usage ==="
az vm list-usage --location eastus --query "[?contains(name.value, 'cores')].{Name:name.localizedValue, Current:currentValue, Limit:limit}" -o table

echo
echo "=== Current AKS Configuration ==="
az aks show --resource-group "$AKS_RESOURCE_GROUP" --name "$AKS_CLUSTER_NAME" --query "agentPoolProfiles[].{Name:name, Count:count, VmSize:vmSize, Mode:mode}" -o table

echo
echo "⚠️  WARNING: This will cause brief downtime for your AKS services!"
echo "The process will:"
echo "1. Delete current node pool (frees 2 vCPUs)"
echo "2. Wait for quota to be available"
echo "3. Create new node pool with 4 vCPUs"
echo

read -p "Do you want to proceed with the upgrade? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Operation cancelled."
    exit 1
fi

echo
echo "🚀 Starting AKS node pool upgrade..."

# Step 1: Delete the current node pool
echo
echo "Step 1: Deleting current node pool to free vCPU quota..."
az aks nodepool delete \
    --resource-group "$AKS_RESOURCE_GROUP" \
    --cluster-name "$AKS_CLUSTER_NAME" \
    --name "$AKS_NODE_POOL_NAME" \
    --yes

if [ $? -ne 0 ]; then
    echo "❌ Failed to delete current node pool!"
    exit 1
fi

echo "✅ Current node pool deleted successfully!"

# Step 2: Wait for quota to be freed
echo
echo "Step 2: Waiting for vCPU quota to be freed..."
for i in {1..6}; do
    echo "Waiting... ($i/6)"
    sleep 30
    
    # Check quota
    CURRENT_USAGE=$(az vm list-usage --location eastus --query "[?contains(name.value, 'cores')].currentValue" -o tsv)
    echo "Current vCPU usage: $CURRENT_USAGE/4"
    
    if [ "$CURRENT_USAGE" -le 2 ]; then
        echo "✅ Sufficient quota available!"
        break
    fi
done

# Step 3: Create new node pool with larger VM size
echo
echo "Step 3: Creating new node pool with Standard_D4s_v3..."
az aks nodepool add \
    --resource-group "$AKS_RESOURCE_GROUP" \
    --cluster-name "$AKS_CLUSTER_NAME" \
    --name "$AKS_NODE_POOL_NAME" \
    --node-count 1 \
    --node-vm-size "$NEW_VM_SIZE" \
    --mode System

if [ $? -ne 0 ]; then
    echo "❌ Failed to create new node pool!"
    echo "⚠️  Your AKS cluster is currently without nodes!"
    echo "Please check Azure Portal or try creating the node pool manually:"
    echo "az aks nodepool add --resource-group $AKS_RESOURCE_GROUP --cluster-name $AKS_CLUSTER_NAME --name $AKS_NODE_POOL_NAME --node-count 1 --node-vm-size $NEW_VM_SIZE --mode System"
    exit 1
fi

echo "✅ New node pool created successfully!"

# Step 4: Wait for new node pool to be ready
echo
echo "Step 4: Waiting for new node pool to be ready..."
sleep 60

# Show final status
echo
echo "=== Final Configuration ==="
echo
echo "AKS Cluster Status:"
az aks show --resource-group "$AKS_RESOURCE_GROUP" --name "$AKS_CLUSTER_NAME" --query "agentPoolProfiles[].{Name:name, Count:count, VmSize:vmSize, Mode:mode}" -o table

echo
echo "Node Status:"
kubectl get nodes -o wide

echo
echo "Final vCPU Usage:"
az vm list-usage --location eastus --query "[?contains(name.value, 'cores')].{Name:name.localizedValue, Current:currentValue, Limit:limit}" -o table

echo
echo "Pod Status (checking for any issues):"
kubectl get pods --all-namespaces --field-selector=status.phase!=Running | head -10

echo
echo "🎉 SUCCESS! AKS node upgraded successfully!"
echo
echo "=== Upgrade Summary ==="
echo "✅ Deleted: Windows VM (freed quota)"
echo "✅ Upgraded: AKS node from Standard_D2s_v3 to Standard_D4s_v3"
echo "✅ CPU Performance: 2x improvement (2 → 4 vCPUs)"
echo "✅ Memory: 2x improvement (8 → 16 GB RAM)"
echo
echo "=== Next Steps ==="
echo "1. Test your web services:"
echo "   curl -I https://nginx.seoedge.pro"
echo "   curl -I https://jenkins.seoedge.pro"
echo "   curl -I https://apach2.seoedge.pro"
echo "2. Monitor performance: kubectl top nodes"
echo "3. Check resource usage: kubectl top pods --all-namespaces"
