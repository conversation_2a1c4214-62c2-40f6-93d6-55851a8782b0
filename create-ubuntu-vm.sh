#!/bin/bash

# Script to create Ubuntu 24.04 Server VM in the same network as AKS cluster

echo "=== Creating Ubuntu 24.04 Server VM for AKS Testing ==="
echo

# Check if network config file exists
if [ ! -f "aks-network-config.env" ]; then
    echo "❌ Network configuration file not found!"
    echo "Please run './get-aks-network-info.sh' first to gather AKS network information."
    exit 1
fi

# Load network configuration
source aks-network-config.env

echo "Using AKS network configuration:"
echo "  AKS Cluster: $AKS_CLUSTER_NAME"
echo "  Location: $LOCATION"
echo "  VNet: $VNET_NAME"
echo "  VNet Resource Group: $VNET_RESOURCE_GROUP"
echo

# VM Configuration
VM_NAME="ubuntu-test-vm"
VM_SIZE="Standard_B2s"  # 2 vCPUs, 4GB RAM - good for testing
ADMIN_USERNAME="azureuser"
VM_RESOURCE_GROUP="$AKS_RESOURCE_GROUP"  # Use same RG as AKS cluster

# Prompt for VM configuration
read -p "Enter VM name [$VM_NAME]: " INPUT_VM_NAME
VM_NAME=${INPUT_VM_NAME:-$VM_NAME}

read -p "Enter VM size [$VM_SIZE]: " INPUT_VM_SIZE
VM_SIZE=${INPUT_VM_SIZE:-$VM_SIZE}

read -p "Enter admin username [$ADMIN_USERNAME]: " INPUT_ADMIN_USERNAME
ADMIN_USERNAME=${INPUT_ADMIN_USERNAME:-$ADMIN_USERNAME}

read -p "Enter resource group for VM [$VM_RESOURCE_GROUP]: " INPUT_VM_RESOURCE_GROUP
VM_RESOURCE_GROUP=${INPUT_VM_RESOURCE_GROUP:-$VM_RESOURCE_GROUP}

echo
echo "VM Configuration:"
echo "  Name: $VM_NAME"
echo "  Size: $VM_SIZE"
echo "  Admin Username: $ADMIN_USERNAME"
echo "  Resource Group: $VM_RESOURCE_GROUP"
echo

# Check if we need to create a new subnet for the VM
echo "=== Subnet Configuration ==="
echo "Available subnets in VNet $VNET_NAME:"
az network vnet subnet list --vnet-name "$VNET_NAME" --resource-group "$VNET_RESOURCE_GROUP" --query "[].{Name:name, AddressPrefix:addressPrefix, AvailableIPs:availableIpAddressCount}" -o table

echo
read -p "Do you want to create a new subnet for the VM? (y/N): " CREATE_NEW_SUBNET

if [[ "$CREATE_NEW_SUBNET" =~ ^[Yy]$ ]]; then
    VM_SUBNET_NAME="vm-subnet"
    read -p "Enter subnet name [$VM_SUBNET_NAME]: " INPUT_SUBNET_NAME
    VM_SUBNET_NAME=${INPUT_SUBNET_NAME:-$VM_SUBNET_NAME}
    
    read -p "Enter subnet address prefix (e.g., ********/24): " SUBNET_ADDRESS_PREFIX
    
    if [ -z "$SUBNET_ADDRESS_PREFIX" ]; then
        echo "❌ Subnet address prefix is required!"
        exit 1
    fi
    
    echo "Creating new subnet: $VM_SUBNET_NAME with prefix: $SUBNET_ADDRESS_PREFIX"
    az network vnet subnet create \
        --resource-group "$VNET_RESOURCE_GROUP" \
        --vnet-name "$VNET_NAME" \
        --name "$VM_SUBNET_NAME" \
        --address-prefixes "$SUBNET_ADDRESS_PREFIX"
    
    if [ $? -ne 0 ]; then
        echo "❌ Failed to create subnet!"
        exit 1
    fi
else
    echo "Available subnets:"
    az network vnet subnet list --vnet-name "$VNET_NAME" --resource-group "$VNET_RESOURCE_GROUP" --query "[].name" -o tsv
    read -p "Enter existing subnet name to use: " VM_SUBNET_NAME
    
    if [ -z "$VM_SUBNET_NAME" ]; then
        echo "❌ Subnet name is required!"
        exit 1
    fi
fi

echo
echo "=== Creating Network Security Group ==="
NSG_NAME="${VM_NAME}-nsg"

# Create NSG
az network nsg create \
    --resource-group "$VM_RESOURCE_GROUP" \
    --name "$NSG_NAME" \
    --location "$LOCATION"

# Add SSH rule
az network nsg rule create \
    --resource-group "$VM_RESOURCE_GROUP" \
    --nsg-name "$NSG_NAME" \
    --name "SSH" \
    --protocol tcp \
    --priority 1001 \
    --destination-port-range 22 \
    --access allow

# Add HTTP rule for web services testing
az network nsg rule create \
    --resource-group "$VM_RESOURCE_GROUP" \
    --nsg-name "$NSG_NAME" \
    --name "HTTP" \
    --protocol tcp \
    --priority 1002 \
    --destination-port-range 80 \
    --access allow

# Add HTTPS rule
az network nsg rule create \
    --resource-group "$VM_RESOURCE_GROUP" \
    --nsg-name "$NSG_NAME" \
    --name "HTTPS" \
    --protocol tcp \
    --priority 1003 \
    --destination-port-range 443 \
    --access allow

# Add custom ports for testing (8080, 3000, 5000)
az network nsg rule create \
    --resource-group "$VM_RESOURCE_GROUP" \
    --nsg-name "$NSG_NAME" \
    --name "CustomPorts" \
    --protocol tcp \
    --priority 1004 \
    --destination-port-ranges 8080 3000 5000 8000 9000 \
    --access allow

echo
echo "=== Creating Public IP ==="
PUBLIC_IP_NAME="${VM_NAME}-ip"

az network public-ip create \
    --resource-group "$VM_RESOURCE_GROUP" \
    --name "$PUBLIC_IP_NAME" \
    --location "$LOCATION" \
    --allocation-method Static \
    --sku Standard

echo
echo "=== Creating Network Interface ==="
NIC_NAME="${VM_NAME}-nic"

az network nic create \
    --resource-group "$VM_RESOURCE_GROUP" \
    --name "$NIC_NAME" \
    --location "$LOCATION" \
    --subnet "$VM_SUBNET_NAME" \
    --vnet-name "$VNET_NAME" \
    --network-security-group "$NSG_NAME" \
    --public-ip-address "$PUBLIC_IP_NAME"

# If VNet is in different resource group, specify it
if [ "$VNET_RESOURCE_GROUP" != "$VM_RESOURCE_GROUP" ]; then
    az network nic create \
        --resource-group "$VM_RESOURCE_GROUP" \
        --name "$NIC_NAME" \
        --location "$LOCATION" \
        --subnet "/subscriptions/$(az account show --query id -o tsv)/resourceGroups/$VNET_RESOURCE_GROUP/providers/Microsoft.Network/virtualNetworks/$VNET_NAME/subnets/$VM_SUBNET_NAME" \
        --network-security-group "$NSG_NAME" \
        --public-ip-address "$PUBLIC_IP_NAME"
fi

echo
echo "=== Creating Virtual Machine ==="

# Generate SSH key if it doesn't exist
if [ ! -f ~/.ssh/id_rsa.pub ]; then
    echo "Generating SSH key pair..."
    ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa -N ""
fi

# Create the VM with Ubuntu 24.04
az vm create \
    --resource-group "$VM_RESOURCE_GROUP" \
    --name "$VM_NAME" \
    --location "$LOCATION" \
    --size "$VM_SIZE" \
    --image "Canonical:0001-com-ubuntu-server-noble:24_04-lts-gen2:latest" \
    --admin-username "$ADMIN_USERNAME" \
    --ssh-key-values ~/.ssh/id_rsa.pub \
    --nics "$NIC_NAME" \
    --os-disk-size-gb 30 \
    --storage-sku Premium_LRS

if [ $? -eq 0 ]; then
    echo
    echo "✅ VM created successfully!"
    
    # Get VM details
    VM_PUBLIC_IP=$(az network public-ip show --resource-group "$VM_RESOURCE_GROUP" --name "$PUBLIC_IP_NAME" --query ipAddress -o tsv)
    VM_PRIVATE_IP=$(az network nic show --resource-group "$VM_RESOURCE_GROUP" --name "$NIC_NAME" --query ipConfigurations[0].privateIpAddress -o tsv)
    
    echo
    echo "=== VM Connection Information ==="
    echo "Public IP: $VM_PUBLIC_IP"
    echo "Private IP: $VM_PRIVATE_IP"
    echo "SSH Command: ssh $ADMIN_USERNAME@$VM_PUBLIC_IP"
    echo
    echo "=== Next Steps ==="
    echo "1. Connect to the VM: ssh $ADMIN_USERNAME@$VM_PUBLIC_IP"
    echo "2. Run the setup script: ./setup-ubuntu-vm.sh"
    echo "3. The VM is in the same network as your AKS cluster and can access internal services"
    
    # Save connection info
    cat > vm-connection-info.txt << EOF
VM Name: $VM_NAME
Resource Group: $VM_RESOURCE_GROUP
Public IP: $VM_PUBLIC_IP
Private IP: $VM_PRIVATE_IP
SSH Command: ssh $ADMIN_USERNAME@$VM_PUBLIC_IP
Admin Username: $ADMIN_USERNAME

Network Information:
VNet: $VNET_NAME
Subnet: $VM_SUBNET_NAME
NSG: $NSG_NAME

Created: $(date)
EOF
    
    echo "Connection information saved to 'vm-connection-info.txt'"
    
else
    echo "❌ Failed to create VM!"
    exit 1
fi
