{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"vmName": {"value": "ubuntu-test-vm"}, "adminUsername": {"value": "azureuser"}, "sshPublicKey": {"value": "REPLACE_WITH_YOUR_SSH_PUBLIC_KEY"}, "vmSize": {"value": "Standard_B2s"}, "existingVnetName": {"value": "REPLACE_WITH_YOUR_VNET_NAME"}, "existingVnetResourceGroup": {"value": "REPLACE_WITH_YOUR_VNET_RESOURCE_GROUP"}, "subnetName": {"value": "vm-subnet"}, "subnetAddressPrefix": {"value": "********/24"}, "createNewSubnet": {"value": true}}}