#!/bin/bash

# Script to get AKS cluster network information
# This will help us identify the virtual network and subnet used by your AKS cluster

echo "=== AKS Cluster Network Information Retrieval ==="
echo

# Check if Azure CLI is installed and logged in
if ! command -v az &> /dev/null; then
    echo "Error: Azure CLI is not installed. Please install it first."
    exit 1
fi

# Check if logged in to Azure
if ! az account show &> /dev/null; then
    echo "Error: Not logged in to Azure. Please run 'az login' first."
    exit 1
fi

echo "Current Azure subscription:"
az account show --query "{name:name, id:id}" -o table
echo

# List all AKS clusters in the subscription
echo "Available AKS clusters:"
AKS_CLUSTERS=$(az aks list --query "[].{Name:name, ResourceGroup:resourceGroup, Location:location}" -o table)
echo "$AKS_CLUSTERS"
echo

# Prompt user to select AKS cluster
read -p "Enter the AKS cluster name: " AKS_CLUSTER_NAME
read -p "Enter the resource group name: " RESOURCE_GROUP_NAME

# Validate cluster exists
if ! az aks show --name "$AKS_CLUSTER_NAME" --resource-group "$RESOURCE_GROUP_NAME" &> /dev/null; then
    echo "Error: AKS cluster '$AKS_CLUSTER_NAME' not found in resource group '$RESOURCE_GROUP_NAME'"
    exit 1
fi

echo "=== Retrieving network information for AKS cluster: $AKS_CLUSTER_NAME ==="
echo

# Get AKS cluster details
AKS_INFO=$(az aks show --name "$AKS_CLUSTER_NAME" --resource-group "$RESOURCE_GROUP_NAME")

# Extract network information
VNET_SUBNET_ID=$(echo "$AKS_INFO" | jq -r '.agentPoolProfiles[0].vnetSubnetId // empty')
NODE_RESOURCE_GROUP=$(echo "$AKS_INFO" | jq -r '.nodeResourceGroup')
LOCATION=$(echo "$AKS_INFO" | jq -r '.location')

echo "AKS Cluster Location: $LOCATION"
echo "Node Resource Group: $NODE_RESOURCE_GROUP"

if [ "$VNET_SUBNET_ID" != "null" ] && [ -n "$VNET_SUBNET_ID" ]; then
    echo "VNet Subnet ID: $VNET_SUBNET_ID"
    
    # Parse VNet and Subnet information
    VNET_RESOURCE_GROUP=$(echo "$VNET_SUBNET_ID" | cut -d'/' -f5)
    VNET_NAME=$(echo "$VNET_SUBNET_ID" | cut -d'/' -f9)
    SUBNET_NAME=$(echo "$VNET_SUBNET_ID" | cut -d'/' -f11)
    
    echo "VNet Resource Group: $VNET_RESOURCE_GROUP"
    echo "VNet Name: $VNET_NAME"
    echo "AKS Subnet Name: $SUBNET_NAME"
    
    # Get VNet details
    echo
    echo "=== Virtual Network Details ==="
    az network vnet show --name "$VNET_NAME" --resource-group "$VNET_RESOURCE_GROUP" --query "{name:name, addressSpace:addressSpace.addressPrefixes, location:location}" -o table
    
    echo
    echo "=== Existing Subnets ==="
    az network vnet subnet list --vnet-name "$VNET_NAME" --resource-group "$VNET_RESOURCE_GROUP" --query "[].{Name:name, AddressPrefix:addressPrefix}" -o table
    
    # Save configuration to file for VM creation
    cat > aks-network-config.env << EOF
# AKS Network Configuration
AKS_CLUSTER_NAME="$AKS_CLUSTER_NAME"
AKS_RESOURCE_GROUP="$RESOURCE_GROUP_NAME"
NODE_RESOURCE_GROUP="$NODE_RESOURCE_GROUP"
LOCATION="$LOCATION"
VNET_RESOURCE_GROUP="$VNET_RESOURCE_GROUP"
VNET_NAME="$VNET_NAME"
AKS_SUBNET_NAME="$SUBNET_NAME"
VNET_SUBNET_ID="$VNET_SUBNET_ID"
EOF
    
    echo
    echo "✅ Network configuration saved to 'aks-network-config.env'"
    echo "You can now run 'create-ubuntu-vm.sh' to create the Ubuntu VM in the same network."
    
else
    echo "⚠️  AKS cluster is using default networking (kubenet)"
    echo "The cluster is likely using the node resource group's VNet."
    
    # Try to find VNet in node resource group
    echo
    echo "=== Searching for VNet in node resource group ==="
    VNETS_IN_NODE_RG=$(az network vnet list --resource-group "$NODE_RESOURCE_GROUP" --query "[].{Name:name, AddressSpace:addressSpace.addressPrefixes[0], Location:location}" -o table)
    
    if [ -n "$VNETS_IN_NODE_RG" ]; then
        echo "$VNETS_IN_NODE_RG"
        
        # Get the first VNet (usually there's only one)
        VNET_NAME=$(az network vnet list --resource-group "$NODE_RESOURCE_GROUP" --query "[0].name" -o tsv)
        
        if [ -n "$VNET_NAME" ]; then
            echo
            echo "=== Subnets in VNet: $VNET_NAME ==="
            az network vnet subnet list --vnet-name "$VNET_NAME" --resource-group "$NODE_RESOURCE_GROUP" --query "[].{Name:name, AddressPrefix:addressPrefix}" -o table
            
            # Save configuration
            cat > aks-network-config.env << EOF
# AKS Network Configuration (Default/Kubenet)
AKS_CLUSTER_NAME="$AKS_CLUSTER_NAME"
AKS_RESOURCE_GROUP="$RESOURCE_GROUP_NAME"
NODE_RESOURCE_GROUP="$NODE_RESOURCE_GROUP"
LOCATION="$LOCATION"
VNET_RESOURCE_GROUP="$NODE_RESOURCE_GROUP"
VNET_NAME="$VNET_NAME"
AKS_SUBNET_NAME=""
VNET_SUBNET_ID=""
EOF
            
            echo
            echo "✅ Network configuration saved to 'aks-network-config.env'"
            echo "You can now run 'create-ubuntu-vm.sh' to create the Ubuntu VM in the same network."
        fi
    else
        echo "❌ No VNet found in node resource group. Manual configuration may be required."
    fi
fi

echo
echo "=== Next Steps ==="
echo "1. Review the network information above"
echo "2. Run './create-ubuntu-vm.sh' to create the Ubuntu 24.04 Server VM"
echo "3. The VM will be created in the same virtual network as your AKS cluster"
