#!/bin/bash

# Simple VM creation script
echo "Creating Ubuntu VM in existing subnet..."

# Load network configuration
source aks-network-config.env

# Generate SSH key if it doesn't exist
if [ ! -f ~/.ssh/id_rsa.pub ]; then
    echo "Generating SSH key pair..."
    ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa -N ""
fi

# Create VM directly
az vm create \
    --resource-group "myResourceGroup" \
    --name "ubuntu-test-vm" \
    --image "Ubuntu2204" \
    --admin-username "azureuser" \
    --ssh-key-values ~/.ssh/id_rsa.pub \
    --subnet "/subscriptions/$(az account show --query id -o tsv)/resourceGroups/MC_myResourceGroup_myAKSCluster_eastus/providers/Microsoft.Network/virtualNetworks/aks-vnet-********/subnets/vm-subnet" \
    --nsg "ubuntu-test-vm-nsg" \
    --public-ip-address "ubuntu-test-vm-ip" \
    --size "Standard_B2s" \
    --location "eastus" \
    --os-disk-size-gb 30 \
    --storage-sku Premium_LRS

if [ $? -eq 0 ]; then
    echo "✅ VM created successfully!"
    
    # Get VM details
    VM_PUBLIC_IP=$(az vm show -d --resource-group "myResourceGroup" --name "ubuntu-test-vm" --query publicIps -o tsv)
    VM_PRIVATE_IP=$(az vm show -d --resource-group "myResourceGroup" --name "ubuntu-test-vm" --query privateIps -o tsv)
    
    echo
    echo "=== VM Connection Information ==="
    echo "VM Name: ubuntu-test-vm"
    echo "Public IP: $VM_PUBLIC_IP"
    echo "Private IP: $VM_PRIVATE_IP"
    echo "SSH Command: ssh azureuser@$VM_PUBLIC_IP"
    
    # Save connection info
    cat > vm-connection-info.txt << EOF
VM Name: ubuntu-test-vm
Resource Group: myResourceGroup
Public IP: $VM_PUBLIC_IP
Private IP: $VM_PRIVATE_IP
SSH Command: ssh azureuser@$VM_PUBLIC_IP
Admin Username: azureuser

Network Information:
VNet: aks-vnet-********
Subnet: vm-subnet

Created: $(date)
EOF
    
    echo "Connection information saved to 'vm-connection-info.txt'"
    echo
    echo "Next steps:"
    echo "1. Connect to VM: ssh azureuser@$VM_PUBLIC_IP"
    echo "2. Copy setup script: scp setup-ubuntu-vm.sh azureuser@$VM_PUBLIC_IP:~/"
    echo "3. Run setup: ssh azureuser@$VM_PUBLIC_IP './setup-ubuntu-vm.sh'"
    
else
    echo "❌ Failed to create VM!"
fi
