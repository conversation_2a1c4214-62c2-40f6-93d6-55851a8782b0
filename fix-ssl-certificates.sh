#!/bin/bash

echo "=== Fixing SSL Certificates for Jenkins and Other Services ==="

# Variables
EMAIL="<EMAIL>"  # Using your domain for the email

echo "🔍 Diagnosing SSL certificate issues..."
echo

# Check current certificate status
echo "=== Current Certificate Status ==="
kubectl get certificates --all-namespaces

echo
echo "=== Current Cluster Issuer Status ==="
kubectl get clusterissuer

echo
echo "🛠️  The issue: letsencrypt-prod cluster issuer has invalid email"
echo "Current email in cluster-issuer.yaml: <EMAIL>"
echo "This needs to be a valid email address for Let's Encrypt"
echo

read -p "Do you want to fix the SSL certificates? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Operation cancelled."
    exit 1
fi

echo
echo "🚀 Starting SSL certificate fix..."

# Step 1: Delete the current broken cluster issuer
echo
echo "Step 1: Removing broken cluster issuer..."
kubectl delete clusterissuer letsencrypt-prod

# Step 2: Create a new cluster issuer with valid email
echo
echo "Step 2: Creating new cluster issuer with valid email..."
cat <<EOF | kubectl apply -f -
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    email: $EMAIL
    server: https://acme-v02.api.letsencrypt.org/directory
    privateKeySecretRef:
      name: letsencrypt-prod-private-key
    solvers:
    - http01:
        ingress:
          class: nginx
EOF

if [ $? -ne 0 ]; then
    echo "❌ Failed to create cluster issuer!"
    exit 1
fi

echo "✅ New cluster issuer created with email: $EMAIL"

# Step 3: Delete existing failed certificates to trigger recreation
echo
echo "Step 3: Deleting failed certificates to trigger recreation..."
kubectl delete certificate apache-tls domain2-tls jenkins-tls

# Step 4: Delete the certificate secrets to force recreation
echo
echo "Step 4: Deleting certificate secrets..."
kubectl delete secret apache-tls domain2-tls jenkins-tls 2>/dev/null || true

# Step 5: Wait for cluster issuer to be ready
echo
echo "Step 5: Waiting for cluster issuer to be ready..."
sleep 30

# Check cluster issuer status
kubectl get clusterissuer letsencrypt-prod

# Step 6: Reapply ingress configurations to trigger certificate creation
echo
echo "Step 6: Reapplying ingress configurations..."

# Reapply Jenkins ingress
kubectl apply -f jenkins-ingress.yaml

# Reapply Apache ingress  
kubectl apply -f apache-ingress.yaml

# Reapply Nginx ingress
kubectl apply -f exported-service.yaml

echo "✅ Ingress configurations reapplied"

# Step 7: Wait for certificates to be issued
echo
echo "Step 7: Waiting for certificates to be issued..."
echo "This may take 2-5 minutes..."

for i in {1..10}; do
    echo "Checking certificate status... ($i/10)"
    sleep 30
    
    # Check certificate status
    READY_CERTS=$(kubectl get certificates --no-headers | grep -c "True")
    TOTAL_CERTS=$(kubectl get certificates --no-headers | wc -l)
    
    echo "Certificates ready: $READY_CERTS/$TOTAL_CERTS"
    
    if [ "$READY_CERTS" -eq "$TOTAL_CERTS" ] && [ "$TOTAL_CERTS" -gt 0 ]; then
        echo "✅ All certificates are ready!"
        break
    fi
    
    if [ $i -eq 10 ]; then
        echo "⚠️  Certificates are taking longer than expected..."
        echo "Let's check the status and continue..."
    fi
done

# Step 8: Show final status
echo
echo "=== Final Certificate Status ==="
kubectl get certificates --all-namespaces

echo
echo "=== Certificate Details ==="
kubectl get certificaterequests --all-namespaces

echo
echo "=== Cluster Issuer Status ==="
kubectl get clusterissuer

# Step 9: Test the services
echo
echo "=== Testing Services ==="
echo "Testing Jenkins (should now have valid SSL)..."
curl -I -k https://jenkins.seoedge.pro

echo
echo "Testing Nginx..."
curl -I -k https://nginx.seoedge.pro

echo
echo "Testing Apache..."
curl -I -k https://apach2.seoedge.pro

echo
echo "🎉 SSL Certificate Fix Complete!"
echo
echo "=== Summary ==="
echo "✅ Fixed cluster issuer email: $EMAIL"
echo "✅ Recreated all certificates"
echo "✅ Reapplied ingress configurations"
echo
echo "=== Next Steps ==="
echo "1. Wait 5-10 minutes for certificates to fully propagate"
echo "2. Test your services in browser:"
echo "   - https://jenkins.seoedge.pro"
echo "   - https://nginx.seoedge.pro"
echo "   - https://apach2.seoedge.pro"
echo "3. If certificates still show as 'False', check:"
echo "   kubectl describe certificate jenkins-tls"
echo "   kubectl logs -n cert-manager deployment/cert-manager"
echo
echo "=== Troubleshooting ==="
echo "If certificates still fail:"
echo "1. Check DNS: nslookup jenkins.seoedge.pro"
echo "2. Check ingress: kubectl get ingress"
echo "3. Check cert-manager logs: kubectl logs -n cert-manager deployment/cert-manager"
