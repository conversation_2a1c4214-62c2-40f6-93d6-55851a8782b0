#!/bin/bash

echo "=== Creating Network Security Group for ubuntu-jump VM ==="

# Variables
VM_NAME="ubuntu-jump"
RESOURCE_GROUP="MC_MYRESOURCEGROUP_MYAKSCLUSTER_EASTUS"
NIC_NAME="ubuntu-jump445_z1"
NSG_NAME="ubuntu-jump-nsg"
LOCATION="eastus"

echo "Creating NSG: $NSG_NAME"

# Create NSG
az network nsg create \
    --resource-group "$RESOURCE_GROUP" \
    --name "$NSG_NAME" \
    --location "$LOCATION"

if [ $? -ne 0 ]; then
    echo "❌ Failed to create NSG!"
    exit 1
fi

echo "✅ NSG created successfully!"

# Add SSH rule
echo "Adding SSH rule..."
az network nsg rule create \
    --resource-group "$RESOURCE_GROUP" \
    --nsg-name "$NSG_NAME" \
    --name "SSH" \
    --protocol tcp \
    --priority 1001 \
    --destination-port-range 22 \
    --access allow \
    --source-address-prefixes "*"

if [ $? -ne 0 ]; then
    echo "❌ Failed to create SSH rule!"
    exit 1
fi

echo "✅ SSH rule created successfully!"

# Associate NSG with network interface
echo "Associating NSG with network interface..."
az network nic update \
    --resource-group "$RESOURCE_GROUP" \
    --name "$NIC_NAME" \
    --network-security-group "$NSG_NAME"

if [ $? -ne 0 ]; then
    echo "❌ Failed to associate NSG with NIC!"
    exit 1
fi

echo "✅ NSG associated with network interface successfully!"

# Get the public IP for testing
PUBLIC_IP=$(az network public-ip show --resource-group "$RESOURCE_GROUP" --name "ubuntu-jump-public-ip" --query ipAddress -o tsv)

echo
echo "🎉 SUCCESS! Network Security Group configured for ubuntu-jump VM"
echo
echo "=== Security Configuration ==="
echo "NSG Name: $NSG_NAME"
echo "SSH Access: Allowed on port 22"
echo "Public IP: $PUBLIC_IP"
echo
echo "=== Test SSH Connection ==="
echo "ssh azureuser@$PUBLIC_IP"
echo
echo "Note: Make sure you have the correct SSH key configured for the VM."
