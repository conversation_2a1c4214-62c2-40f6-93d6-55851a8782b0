#!/bin/bash

echo "=== Fixing Ingress PathType for SSL Certificate Challenges ==="

echo "🔍 The issue: NGINX ingress controller is rejecting ACME challenge paths"
echo "Error: 'path /.well-known/acme-challenge/... cannot be used with pathType Exact'"
echo "Solution: Change pathType from 'Exact' to 'Prefix' in all ingress configurations"
echo

read -p "Do you want to fix the ingress pathType configurations? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Operation cancelled."
    exit 1
fi

echo
echo "🚀 Fixing ingress configurations..."

# Fix Jenkins ingress
echo "Fixing Jenkins ingress..."
cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: jenkins-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod 
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
spec:
  tls:
  - hosts:
    - jenkins.seoedge.pro
    secretName: jenkins-tls
  rules:
  - host: jenkins.seoedge.pro
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: jenkins
            port:
              number: 8080
EOF

# Fix Apache ingress
echo "Fixing Apache ingress..."
cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: apache-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - apach2.seoedge.pro
    secretName: apache-tls
  rules:
  - host: apach2.seoedge.pro
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: apache-service
            port:
              number: 80
EOF

# Fix Nginx ingress
echo "Fixing Nginx ingress..."
cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: hello-world-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - nginx.seoedge.pro
    secretName: domain2-tls
  rules:
  - host: nginx.seoedge.pro
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: hello-world
            port:
              number: 80
EOF

echo "✅ All ingress configurations updated with pathType: Prefix"

# Delete existing failed challenges to trigger recreation
echo
echo "Deleting failed challenges to trigger recreation..."
kubectl delete challenges --all

# Delete certificate requests to trigger recreation
echo "Deleting certificate requests to trigger recreation..."
kubectl delete certificaterequests --all

# Delete certificates to trigger recreation
echo "Deleting certificates to trigger recreation..."
kubectl delete certificates --all

# Delete certificate secrets
echo "Deleting certificate secrets..."
kubectl delete secret apache-tls domain2-tls jenkins-tls 2>/dev/null || true

echo
echo "Waiting 30 seconds for resources to be cleaned up..."
sleep 30

# Trigger certificate recreation by annotating ingresses
echo "Triggering certificate recreation..."
kubectl annotate ingress jenkins-ingress cert-manager.io/force-renewal=$(date +%s) --overwrite
kubectl annotate ingress apache-ingress cert-manager.io/force-renewal=$(date +%s) --overwrite
kubectl annotate ingress hello-world-ingress cert-manager.io/force-renewal=$(date +%s) --overwrite

echo
echo "Waiting for new certificates to be created..."
sleep 60

# Check status
echo
echo "=== Current Certificate Status ==="
kubectl get certificates

echo
echo "=== Current Challenge Status ==="
kubectl get challenges

echo
echo "=== Current Certificate Requests ==="
kubectl get certificaterequests

echo
echo "🎉 Ingress PathType Fix Complete!"
echo
echo "=== What was fixed ==="
echo "✅ Changed pathType from 'Exact' to 'Prefix' in all ingress configurations"
echo "✅ Deleted and recreated all certificates and challenges"
echo "✅ ACME challenge paths should now work properly"
echo
echo "=== Next Steps ==="
echo "1. Wait 5-10 minutes for Let's Encrypt to validate domains"
echo "2. Monitor certificate status: kubectl get certificates"
echo "3. Check challenges: kubectl get challenges"
echo "4. Test services once certificates are ready:"
echo "   - https://jenkins.seoedge.pro"
echo "   - https://nginx.seoedge.pro"
echo "   - https://apach2.seoedge.pro"
echo
echo "=== Monitoring Commands ==="
echo "kubectl get certificates --watch"
echo "kubectl describe certificate jenkins-tls"
echo "kubectl logs -n cert-manager deployment/cert-manager --tail=20"
