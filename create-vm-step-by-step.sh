#!/bin/bash

echo "Creating Ubuntu VM step by step..."

# Load network configuration
source aks-network-config.env

# Variables
VM_NAME="ubuntu-test-vm"
RESOURCE_GROUP="myResourceGroup"
LOCATION="eastus"
ADMIN_USERNAME="azureuser"
VM_SIZE="Standard_B2s"

# Generate SSH key if it doesn't exist
if [ ! -f ~/.ssh/id_rsa.pub ]; then
    echo "Generating SSH key pair..."
    ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa -N ""
fi

echo "Step 1: Creating Network Security Group..."
az network nsg create \
    --resource-group "$RESOURCE_GROUP" \
    --name "${VM_NAME}-nsg" \
    --location "$LOCATION"

echo "Step 2: Adding NSG rules..."
# SSH rule
az network nsg rule create \
    --resource-group "$RESOURCE_GROUP" \
    --nsg-name "${VM_NAME}-nsg" \
    --name "SSH" \
    --protocol tcp \
    --priority 1001 \
    --destination-port-range 22 \
    --access allow

# HTTP rule
az network nsg rule create \
    --resource-group "$RESOURCE_GROUP" \
    --nsg-name "${VM_NAME}-nsg" \
    --name "HTTP" \
    --protocol tcp \
    --priority 1002 \
    --destination-port-range 80 \
    --access allow

echo "Step 3: Creating Public IP..."
az network public-ip create \
    --resource-group "$RESOURCE_GROUP" \
    --name "${VM_NAME}-ip" \
    --location "$LOCATION" \
    --allocation-method Static \
    --sku Standard

echo "Step 4: Creating Network Interface..."
az network nic create \
    --resource-group "$RESOURCE_GROUP" \
    --name "${VM_NAME}-nic" \
    --location "$LOCATION" \
    --subnet "/subscriptions/$(az account show --query id -o tsv)/resourceGroups/$VNET_RESOURCE_GROUP/providers/Microsoft.Network/virtualNetworks/$VNET_NAME/subnets/vm-subnet" \
    --network-security-group "${VM_NAME}-nsg" \
    --public-ip-address "${VM_NAME}-ip"

echo "Step 5: Creating Virtual Machine..."
az vm create \
    --resource-group "$RESOURCE_GROUP" \
    --name "$VM_NAME" \
    --location "$LOCATION" \
    --size "$VM_SIZE" \
    --image "Ubuntu2204" \
    --admin-username "$ADMIN_USERNAME" \
    --ssh-key-values ~/.ssh/id_rsa.pub \
    --nics "${VM_NAME}-nic" \
    --os-disk-size-gb 30 \
    --storage-sku Premium_LRS

if [ $? -eq 0 ]; then
    echo "✅ VM created successfully!"
    
    # Get VM details
    echo "Getting VM details..."
    VM_PUBLIC_IP=$(az network public-ip show --resource-group "$RESOURCE_GROUP" --name "${VM_NAME}-ip" --query ipAddress -o tsv)
    VM_PRIVATE_IP=$(az network nic show --resource-group "$RESOURCE_GROUP" --name "${VM_NAME}-nic" --query ipConfigurations[0].privateIpAddress -o tsv)
    
    echo
    echo "=== VM Connection Information ==="
    echo "VM Name: $VM_NAME"
    echo "Public IP: $VM_PUBLIC_IP"
    echo "Private IP: $VM_PRIVATE_IP"
    echo "SSH Command: ssh $ADMIN_USERNAME@$VM_PUBLIC_IP"
    
    # Save connection info
    cat > vm-connection-info.txt << EOF
VM Name: $VM_NAME
Resource Group: $RESOURCE_GROUP
Public IP: $VM_PUBLIC_IP
Private IP: $VM_PRIVATE_IP
SSH Command: ssh $ADMIN_USERNAME@$VM_PUBLIC_IP
Admin Username: $ADMIN_USERNAME

Network Information:
VNet: $VNET_NAME
VNet Resource Group: $VNET_RESOURCE_GROUP
Subnet: vm-subnet

Created: $(date)
EOF
    
    echo "Connection information saved to 'vm-connection-info.txt'"
    echo
    echo "=== Next Steps ==="
    echo "1. Connect to VM: ssh $ADMIN_USERNAME@$VM_PUBLIC_IP"
    echo "2. Copy setup script: scp setup-ubuntu-vm.sh $ADMIN_USERNAME@$VM_PUBLIC_IP:~/"
    echo "3. Run setup: ssh $ADMIN_USERNAME@$VM_PUBLIC_IP './setup-ubuntu-vm.sh'"
    
else
    echo "❌ Failed to create VM!"
    exit 1
fi
