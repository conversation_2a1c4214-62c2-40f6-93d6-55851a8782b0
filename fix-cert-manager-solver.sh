#!/bin/bash

echo "=== Fixing cert-manager HTTP01 Solver Configuration ==="

echo "🔍 The issue: cert-manager is creating challenge ingresses with pathType: Exact"
echo "Solution: Update cluster issuer to use ingressClassName instead of class annotation"
echo

read -p "Do you want to fix the cert-manager solver configuration? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Operation cancelled."
    exit 1
fi

echo
echo "🚀 Updating cluster issuer configuration..."

# Delete the current cluster issuer
kubectl delete clusterissuer letsencrypt-prod

# Create new cluster issuer with updated solver configuration
cat <<EOF | kubectl apply -f -
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    email: <EMAIL>
    server: https://acme-v02.api.letsencrypt.org/directory
    privateKeySecretRef:
      name: letsencrypt-prod-private-key
    solvers:
    - http01:
        ingress:
          ingressClassName: nginx
          podTemplate:
            spec:
              nodeSelector:
                "kubernetes.io/os": linux
EOF

echo "✅ Updated cluster issuer with ingressClassName"

# Also update all ingress resources to use ingressClassName instead of annotation
echo
echo "Updating ingress resources to use ingressClassName..."

# Update Jenkins ingress
cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: jenkins-ingress
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod 
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - jenkins.seoedge.pro
    secretName: jenkins-tls
  rules:
  - host: jenkins.seoedge.pro
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: jenkins
            port:
              number: 8080
EOF

# Update Apache ingress
cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: apache-ingress
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - apach2.seoedge.pro
    secretName: apache-tls
  rules:
  - host: apach2.seoedge.pro
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: apache-service
            port:
              number: 80
EOF

# Update Nginx ingress
cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: hello-world-ingress
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - nginx.seoedge.pro
    secretName: domain2-tls
  rules:
  - host: nginx.seoedge.pro
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: hello-world
            port:
              number: 80
EOF

echo "✅ All ingress resources updated with ingressClassName"

# Clean up existing resources
echo
echo "Cleaning up existing certificates and challenges..."
kubectl delete challenges --all
kubectl delete certificaterequests --all
kubectl delete certificates --all
kubectl delete secret apache-tls domain2-tls jenkins-tls 2>/dev/null || true

echo
echo "Waiting 30 seconds for cleanup..."
sleep 30

# Force certificate recreation
echo "Forcing certificate recreation..."
kubectl annotate ingress jenkins-ingress cert-manager.io/force-renewal=$(date +%s) --overwrite
kubectl annotate ingress apache-ingress cert-manager.io/force-renewal=$(date +%s) --overwrite
kubectl annotate ingress hello-world-ingress cert-manager.io/force-renewal=$(date +%s) --overwrite

echo
echo "Waiting for new certificates to be created..."
sleep 60

# Check status
echo
echo "=== Current Status ==="
echo "Cluster Issuer:"
kubectl get clusterissuer

echo
echo "Certificates:"
kubectl get certificates

echo
echo "Challenges:"
kubectl get challenges

echo
echo "Certificate Requests:"
kubectl get certificaterequests

echo
echo "🎉 Cert-manager Solver Configuration Updated!"
echo
echo "=== What was changed ==="
echo "✅ Updated cluster issuer to use ingressClassName instead of class annotation"
echo "✅ Updated all ingress resources to use ingressClassName: nginx"
echo "✅ Added nodeSelector for Linux nodes"
echo "✅ Cleaned up and recreated all certificates"
echo
echo "=== Next Steps ==="
echo "1. Wait 5-10 minutes for Let's Encrypt validation"
echo "2. Monitor: kubectl get certificates --watch"
echo "3. Check logs: kubectl logs -n cert-manager deployment/cert-manager --tail=20"
echo "4. Test services once ready:"
echo "   - https://jenkins.seoedge.pro"
echo "   - https://nginx.seoedge.pro"
echo "   - https://apach2.seoedge.pro"
