{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"vmName": {"type": "string", "defaultValue": "ubuntu-test-vm", "metadata": {"description": "Name of the virtual machine"}}, "adminUsername": {"type": "string", "defaultValue": "azureuser", "metadata": {"description": "Admin username for the VM"}}, "sshPublicKey": {"type": "string", "metadata": {"description": "SSH public key for authentication"}}, "vmSize": {"type": "string", "defaultValue": "Standard_B2s", "allowedValues": ["Standard_B1s", "Standard_B2s", "Standard_B4ms", "Standard_D2s_v3", "Standard_D4s_v3"], "metadata": {"description": "Size of the virtual machine"}}, "existingVnetName": {"type": "string", "metadata": {"description": "Name of the existing virtual network"}}, "existingVnetResourceGroup": {"type": "string", "metadata": {"description": "Resource group of the existing virtual network"}}, "subnetName": {"type": "string", "defaultValue": "vm-subnet", "metadata": {"description": "Name of the subnet for the VM"}}, "subnetAddressPrefix": {"type": "string", "defaultValue": "********/24", "metadata": {"description": "Address prefix for the VM subnet (only used if creating new subnet)"}}, "createNewSubnet": {"type": "bool", "defaultValue": true, "metadata": {"description": "Whether to create a new subnet or use existing one"}}}, "variables": {"location": "[resourceGroup().location]", "networkSecurityGroupName": "[concat(parameters('vmName'), '-nsg')]", "publicIPAddressName": "[concat(parameters('vmName'), '-ip')]", "networkInterfaceName": "[concat(parameters('vmName'), '-nic')]", "osDiskName": "[concat(parameters('vmName'), '-osdisk')]", "vnetId": "[resourceId(parameters('existingVnetResourceGroup'), 'Microsoft.Network/virtualNetworks', parameters('existingVnetName'))]", "subnetRef": "[concat(variables('vnetId'), '/subnets/', parameters('subnetName'))]"}, "resources": [{"condition": "[parameters('createNewSubnet')]", "type": "Microsoft.Network/virtualNetworks/subnets", "apiVersion": "2021-02-01", "name": "[concat(parameters('existingVnetName'), '/', parameters('subnetName'))]", "properties": {"addressPrefix": "[parameters('subnetAddressPrefix')]"}}, {"type": "Microsoft.Network/networkSecurityGroups", "apiVersion": "2021-02-01", "name": "[variables('networkSecurityGroupName')]", "location": "[variables('location')]", "properties": {"securityRules": [{"name": "SSH", "properties": {"priority": 1001, "protocol": "TCP", "access": "Allow", "direction": "Inbound", "sourceAddressPrefix": "*", "sourcePortRange": "*", "destinationAddressPrefix": "*", "destinationPortRange": "22"}}, {"name": "HTTP", "properties": {"priority": 1002, "protocol": "TCP", "access": "Allow", "direction": "Inbound", "sourceAddressPrefix": "*", "sourcePortRange": "*", "destinationAddressPrefix": "*", "destinationPortRange": "80"}}, {"name": "HTTPS", "properties": {"priority": 1003, "protocol": "TCP", "access": "Allow", "direction": "Inbound", "sourceAddressPrefix": "*", "sourcePortRange": "*", "destinationAddressPrefix": "*", "destinationPortRange": "443"}}, {"name": "CustomPorts", "properties": {"priority": 1004, "protocol": "TCP", "access": "Allow", "direction": "Inbound", "sourceAddressPrefix": "*", "sourcePortRange": "*", "destinationAddressPrefix": "*", "destinationPortRanges": ["8080", "3000", "5000", "8000", "9000"]}}]}}, {"type": "Microsoft.Network/publicIPAddresses", "apiVersion": "2021-02-01", "name": "[variables('publicIPAddressName')]", "location": "[variables('location')]", "sku": {"name": "Standard"}, "properties": {"publicIPAllocationMethod": "Static"}}, {"type": "Microsoft.Network/networkInterfaces", "apiVersion": "2021-02-01", "name": "[variables('networkInterfaceName')]", "location": "[variables('location')]", "dependsOn": ["[resourceId('Microsoft.Network/networkSecurityGroups', variables('networkSecurityGroupName'))]", "[resourceId('Microsoft.Network/publicIPAddresses', variables('publicIPAddressName'))]"], "properties": {"ipConfigurations": [{"name": "ipconfig1", "properties": {"subnet": {"id": "[variables('subnetRef')]"}, "privateIPAllocationMethod": "Dynamic", "publicIPAddress": {"id": "[resourceId('Microsoft.Network/publicIPAddresses', variables('publicIPAddressName'))]"}}}], "networkSecurityGroup": {"id": "[resourceId('Microsoft.Network/networkSecurityGroups', variables('networkSecurityGroupName'))]"}}}, {"type": "Microsoft.Compute/virtualMachines", "apiVersion": "2021-03-01", "name": "[parameters('vmName')]", "location": "[variables('location')]", "dependsOn": ["[resourceId('Microsoft.Network/networkInterfaces', variables('networkInterfaceName'))]"], "properties": {"hardwareProfile": {"vmSize": "[parameters('vmSize')]"}, "storageProfile": {"osDisk": {"name": "[variables('osDiskName')]", "caching": "ReadWrite", "createOption": "FromImage", "managedDisk": {"storageAccountType": "Premium_LRS"}, "diskSizeGB": 30}, "imageReference": {"publisher": "Canonical", "offer": "0001-com-ubuntu-server-noble", "sku": "24_04-lts-gen2", "version": "latest"}}, "networkProfile": {"networkInterfaces": [{"id": "[resourceId('Microsoft.Network/networkInterfaces', variables('networkInterfaceName'))]"}]}, "osProfile": {"computerName": "[parameters('vmName')]", "adminUsername": "[parameters('adminUsername')]", "linuxConfiguration": {"disablePasswordAuthentication": true, "ssh": {"publicKeys": [{"path": "[concat('/home/', parameters('adminUsername'), '/.ssh/authorized_keys')]", "keyData": "[parameters('sshPublicKey')]"}]}}}}}], "outputs": {"vmName": {"type": "string", "value": "[parameters('vmName')]"}, "publicIPAddress": {"type": "string", "value": "[reference(resourceId('Microsoft.Network/publicIPAddresses', variables('publicIPAddressName'))).ipAddress]"}, "privateIPAddress": {"type": "string", "value": "[reference(resourceId('Microsoft.Network/networkInterfaces', variables('networkInterfaceName'))).ipConfigurations[0].properties.privateIPAddress]"}, "sshCommand": {"type": "string", "value": "[concat('ssh ', parameters('adminUsername'), '@', reference(resourceId('Microsoft.Network/publicIPAddresses', variables('publicIPAddressName'))).ipAddress)]"}}}