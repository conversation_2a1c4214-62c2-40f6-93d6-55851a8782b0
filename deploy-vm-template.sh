#!/bin/bash

# Deploy Ubuntu VM using ARM template
# Alternative to the create-ubuntu-vm.sh script

echo "=== Deploying Ubuntu VM using ARM Template ==="
echo

# Check if network config exists
if [ ! -f "aks-network-config.env" ]; then
    echo "❌ Network configuration file not found!"
    echo "Please run './get-aks-network-info.sh' first to gather AKS network information."
    exit 1
fi

# Load network configuration
source aks-network-config.env

echo "Using AKS network configuration:"
echo "  VNet: $VNET_NAME"
echo "  VNet Resource Group: $VNET_RESOURCE_GROUP"
echo "  Location: $LOCATION"
echo

# Check if SSH key exists
if [ ! -f ~/.ssh/id_rsa.pub ]; then
    echo "Generating SSH key pair..."
    ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa -N ""
fi

SSH_PUBLIC_KEY=$(cat ~/.ssh/id_rsa.pub)

# Prompt for deployment details
read -p "Enter resource group for VM deployment [$AKS_RESOURCE_GROUP]: " VM_RESOURCE_GROUP
VM_RESOURCE_GROUP=${VM_RESOURCE_GROUP:-$AKS_RESOURCE_GROUP}

read -p "Enter VM name [ubuntu-test-vm]: " VM_NAME
VM_NAME=${VM_NAME:-ubuntu-test-vm}

read -p "Enter admin username [azureuser]: " ADMIN_USERNAME
ADMIN_USERNAME=${ADMIN_USERNAME:-azureuser}

read -p "Enter VM size [Standard_B2s]: " VM_SIZE
VM_SIZE=${VM_SIZE:-Standard_B2s}

read -p "Enter subnet name for VM [vm-subnet]: " SUBNET_NAME
SUBNET_NAME=${SUBNET_NAME:-vm-subnet}

read -p "Enter subnet address prefix [********/24]: " SUBNET_PREFIX
SUBNET_PREFIX=${SUBNET_PREFIX:-********/24}

read -p "Create new subnet? (y/N): " CREATE_SUBNET
if [[ "$CREATE_SUBNET" =~ ^[Yy]$ ]]; then
    CREATE_NEW_SUBNET=true
else
    CREATE_NEW_SUBNET=false
    echo "Available subnets:"
    az network vnet subnet list --vnet-name "$VNET_NAME" --resource-group "$VNET_RESOURCE_GROUP" --query "[].name" -o tsv
    read -p "Enter existing subnet name: " SUBNET_NAME
fi

echo
echo "Deployment Configuration:"
echo "  Resource Group: $VM_RESOURCE_GROUP"
echo "  VM Name: $VM_NAME"
echo "  VM Size: $VM_SIZE"
echo "  Admin Username: $ADMIN_USERNAME"
echo "  VNet: $VNET_NAME (in $VNET_RESOURCE_GROUP)"
echo "  Subnet: $SUBNET_NAME"
echo "  Create New Subnet: $CREATE_NEW_SUBNET"
echo

read -p "Proceed with deployment? (y/N): " CONFIRM
if [[ ! "$CONFIRM" =~ ^[Yy]$ ]]; then
    echo "Deployment cancelled."
    exit 0
fi

# Create deployment name with timestamp
DEPLOYMENT_NAME="ubuntu-vm-$(date +%Y%m%d-%H%M%S)"

echo "Starting ARM template deployment..."
echo "Deployment name: $DEPLOYMENT_NAME"

# Deploy using Azure CLI
az deployment group create \
    --resource-group "$VM_RESOURCE_GROUP" \
    --name "$DEPLOYMENT_NAME" \
    --template-file ubuntu-vm-template.json \
    --parameters \
        vmName="$VM_NAME" \
        adminUsername="$ADMIN_USERNAME" \
        sshPublicKey="$SSH_PUBLIC_KEY" \
        vmSize="$VM_SIZE" \
        existingVnetName="$VNET_NAME" \
        existingVnetResourceGroup="$VNET_RESOURCE_GROUP" \
        subnetName="$SUBNET_NAME" \
        subnetAddressPrefix="$SUBNET_PREFIX" \
        createNewSubnet=$CREATE_NEW_SUBNET

if [ $? -eq 0 ]; then
    echo
    echo "✅ Deployment completed successfully!"
    
    # Get deployment outputs
    echo "Retrieving deployment outputs..."
    OUTPUTS=$(az deployment group show --resource-group "$VM_RESOURCE_GROUP" --name "$DEPLOYMENT_NAME" --query properties.outputs)
    
    VM_PUBLIC_IP=$(echo "$OUTPUTS" | jq -r '.publicIPAddress.value')
    VM_PRIVATE_IP=$(echo "$OUTPUTS" | jq -r '.privateIPAddress.value')
    SSH_COMMAND=$(echo "$OUTPUTS" | jq -r '.sshCommand.value')
    
    echo
    echo "=== VM Connection Information ==="
    echo "VM Name: $VM_NAME"
    echo "Public IP: $VM_PUBLIC_IP"
    echo "Private IP: $VM_PRIVATE_IP"
    echo "SSH Command: $SSH_COMMAND"
    
    # Save connection info
    cat > vm-connection-info.txt << EOF
VM Name: $VM_NAME
Resource Group: $VM_RESOURCE_GROUP
Public IP: $VM_PUBLIC_IP
Private IP: $VM_PRIVATE_IP
SSH Command: $SSH_COMMAND
Admin Username: $ADMIN_USERNAME

Network Information:
VNet: $VNET_NAME
VNet Resource Group: $VNET_RESOURCE_GROUP
Subnet: $SUBNET_NAME

Deployment: $DEPLOYMENT_NAME
Created: $(date)
EOF
    
    echo
    echo "✅ Connection information saved to 'vm-connection-info.txt'"
    echo
    echo "=== Next Steps ==="
    echo "1. Connect to the VM: $SSH_COMMAND"
    echo "2. Copy and run the setup script:"
    echo "   scp setup-ubuntu-vm.sh $ADMIN_USERNAME@$VM_PUBLIC_IP:~/"
    echo "   ssh $ADMIN_USERNAME@$VM_PUBLIC_IP './setup-ubuntu-vm.sh'"
    echo "3. Configure kubectl to connect to your AKS cluster"
    echo "4. Test your services using the provided scripts"
    
else
    echo "❌ Deployment failed!"
    echo "Check the Azure portal or run the following command for details:"
    echo "az deployment group show --resource-group $VM_RESOURCE_GROUP --name $DEPLOYMENT_NAME"
    exit 1
fi
