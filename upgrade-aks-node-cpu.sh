#!/bin/bash

echo "=== Upgrading AKS Node CPU Size ==="

# Variables
CLUSTER_NAME="myAKSCluster"
RESOURCE_GROUP="myResourceGroup"
NODE_POOL_NAME="nodepool1"
CURRENT_VM_SIZE="Standard_D2s_v3"
NEW_VM_SIZE="Standard_D4s_v3"

echo "Cluster: $CLUSTER_NAME"
echo "Resource Group: $RESOURCE_GROUP"
echo "Node Pool: $NODE_POOL_NAME"
echo "Current VM Size: $CURRENT_VM_SIZE (2 vCPUs, 8 GB RAM)"
echo "New VM Size: $NEW_VM_SIZE (4 vCPUs, 16 GB RAM)"
echo

# Show current configuration
echo "=== Current Configuration ==="
az aks show --resource-group "$RESOURCE_GROUP" --name "$CLUSTER_NAME" --query "agentPoolProfiles[].{Name:name, Count:count, VmSize:vmSize, Mode:mode}" -o table

echo
echo "=== Available VM Size Options ==="
echo "Standard_D2s_v3: 2 vCPUs, 8 GB RAM   (Current)"
echo "Standard_D4s_v3: 4 vCPUs, 16 GB RAM  (Recommended upgrade)"
echo "Standard_D8s_v3: 8 vCPUs, 32 GB RAM  (Higher performance)"
echo

echo "🚀 Starting node pool upgrade..."
echo "⚠️  Note: This will cause a brief service interruption as the node is replaced."

# Upgrade the existing node pool to the new VM size
echo
echo "Step 1: Upgrading node pool to larger VM size..."
echo "This will replace the existing node with a new one of the larger size..."

az aks nodepool update \
    --resource-group "$RESOURCE_GROUP" \
    --cluster-name "$CLUSTER_NAME" \
    --name "$NODE_POOL_NAME" \
    --node-vm-size "$NEW_VM_SIZE"

if [ $? -ne 0 ]; then
    echo "❌ Node pool update failed. Trying alternative method..."
    echo "Creating new node pool and removing old one..."

    # Alternative: Create new node pool and delete old one
    az aks nodepool add \
        --resource-group "$RESOURCE_GROUP" \
        --cluster-name "$CLUSTER_NAME" \
        --name "nodepool2" \
        --node-count 1 \
        --node-vm-size "$NEW_VM_SIZE" \
        --mode System

    if [ $? -ne 0 ]; then
        echo "❌ Failed to create new node pool!"
        exit 1
    fi

    echo "✅ New node pool created! Waiting for it to be ready..."
    sleep 60

    # Delete old node pool
    az aks nodepool delete \
        --resource-group "$RESOURCE_GROUP" \
        --cluster-name "$CLUSTER_NAME" \
        --name "$NODE_POOL_NAME" \
        --yes \
        --no-wait

    NODE_POOL_NAME="nodepool2"
    echo "✅ Switched to new node pool: $NODE_POOL_NAME"
else
    echo "✅ Node pool upgraded successfully!"
fi

# Wait for changes to propagate
echo
echo "Step 2: Waiting for changes to propagate..."
sleep 30

# Show final configuration
echo
echo "=== Final Configuration ==="
az aks show --resource-group "$RESOURCE_GROUP" --name "$CLUSTER_NAME" --query "agentPoolProfiles[].{Name:name, Count:count, VmSize:vmSize, Mode:mode}" -o table

echo
echo "=== Node Status ==="
kubectl get nodes -o wide

echo
echo "=== Pod Status ==="
kubectl get pods --all-namespaces | grep -v Running | head -10

echo
echo "🎉 SUCCESS! AKS node CPU upgrade completed!"
echo
echo "=== Upgrade Summary ==="
echo "✅ Old VM Size: $CURRENT_VM_SIZE (2 vCPUs, 8 GB RAM)"
echo "✅ New VM Size: $NEW_VM_SIZE (4 vCPUs, 16 GB RAM)"
echo "✅ CPU Performance: 2x improvement"
echo "✅ Memory: 2x improvement"
echo
echo "=== Cost Impact ==="
echo "⚠️  Note: The new VM size will cost approximately 2x more than the previous size."
echo "💰 Consider monitoring your Azure costs in the Azure Portal."
echo
echo "=== Next Steps ==="
echo "1. Monitor your applications to ensure they're running properly"
echo "2. Check resource utilization: kubectl top nodes"
echo "3. Test your web services:"
echo "   curl -I https://nginx.seoedge.pro"
echo "   curl -I https://jenkins.seoedge.pro"
echo "   curl -I https://apach2.seoedge.pro"
