#!/bin/bash

echo "=== Associating Public IP to ubuntu-jump VM ==="

# Variables
VM_NAME="ubuntu-jump"
RESOURCE_GROUP="MC_MYRESOURCEGROUP_MYAKSCLUSTER_EASTUS"
NIC_NAME="ubuntu-jump445_z1"
PUBLIC_IP_NAME="ubuntu-jump-public-ip"
LOCATION="eastus"

echo "VM Name: $VM_NAME"
echo "Resource Group: $RESOURCE_GROUP"
echo "Network Interface: $NIC_NAME"
echo "Location: $LOCATION"
echo

# Step 1: Create a public IP address
echo "Step 1: Creating public IP address..."
az network public-ip create \
    --resource-group "$RESOURCE_GROUP" \
    --name "$PUBLIC_IP_NAME" \
    --location "$LOCATION" \
    --allocation-method Static \
    --sku Standard

if [ $? -ne 0 ]; then
    echo "❌ Failed to create public IP!"
    exit 1
fi

echo "✅ Public IP created successfully!"

# Step 2: Get the current NIC configuration
echo
echo "Step 2: Getting current network interface configuration..."
NIC_CONFIG=$(az network nic show --resource-group "$RESOURCE_GROUP" --name "$NIC_NAME")

if [ $? -ne 0 ]; then
    echo "❌ Failed to get network interface configuration!"
    exit 1
fi

# Step 3: Associate the public IP with the network interface
echo
echo "Step 3: Associating public IP with network interface..."
az network nic ip-config update \
    --resource-group "$RESOURCE_GROUP" \
    --nic-name "$NIC_NAME" \
    --name "ipconfig1" \
    --public-ip-address "$PUBLIC_IP_NAME"

if [ $? -ne 0 ]; then
    echo "❌ Failed to associate public IP!"
    exit 1
fi

echo "✅ Public IP associated successfully!"

# Step 4: Get the public IP address
echo
echo "Step 4: Getting the assigned public IP address..."
PUBLIC_IP_ADDRESS=$(az network public-ip show --resource-group "$RESOURCE_GROUP" --name "$PUBLIC_IP_NAME" --query ipAddress -o tsv)

if [ -z "$PUBLIC_IP_ADDRESS" ]; then
    echo "❌ Failed to get public IP address!"
    exit 1
fi

# Step 5: Get the private IP address
echo "Getting private IP address..."
PRIVATE_IP_ADDRESS=$(az network nic show --resource-group "$RESOURCE_GROUP" --name "$NIC_NAME" --query ipConfigurations[0].privateIpAddress -o tsv)

# Step 6: Display connection information
echo
echo "🎉 SUCCESS! Public IP has been associated with ubuntu-jump VM"
echo
echo "=== Connection Information ==="
echo "VM Name: $VM_NAME"
echo "Public IP: $PUBLIC_IP_ADDRESS"
echo "Private IP: $PRIVATE_IP_ADDRESS"
echo "SSH Command: ssh azureuser@$PUBLIC_IP_ADDRESS"
echo

# Step 7: Save connection information to file
cat > ubuntu-jump-connection-info.txt << EOF
Ubuntu Jump Server Connection Information
========================================

VM Name: $VM_NAME
Resource Group: $RESOURCE_GROUP
Public IP: $PUBLIC_IP_ADDRESS
Private IP: $PRIVATE_IP_ADDRESS
Network Interface: $NIC_NAME
Public IP Resource: $PUBLIC_IP_NAME

SSH Connection:
ssh azureuser@$PUBLIC_IP_ADDRESS

Created: $(date)

Note: Make sure your Network Security Group allows SSH (port 22) access.
EOF

echo "✅ Connection information saved to 'ubuntu-jump-connection-info.txt'"

# Step 8: Check if NSG allows SSH access
echo
echo "Step 8: Checking Network Security Group rules..."
NSG_NAME=$(az network nic show --resource-group "$RESOURCE_GROUP" --name "$NIC_NAME" --query networkSecurityGroup.id -o tsv | cut -d'/' -f9)

if [ -n "$NSG_NAME" ]; then
    echo "Network Security Group: $NSG_NAME"
    
    # Check if SSH rule exists
    SSH_RULE=$(az network nsg rule list --resource-group "$RESOURCE_GROUP" --nsg-name "$NSG_NAME" --query "[?destinationPortRange=='22' && access=='Allow'].name" -o tsv)
    
    if [ -n "$SSH_RULE" ]; then
        echo "✅ SSH access is allowed (Rule: $SSH_RULE)"
    else
        echo "⚠️  No SSH rule found. Creating SSH rule..."
        az network nsg rule create \
            --resource-group "$RESOURCE_GROUP" \
            --nsg-name "$NSG_NAME" \
            --name "SSH" \
            --protocol tcp \
            --priority 1001 \
            --destination-port-range 22 \
            --access allow \
            --source-address-prefixes "*"
        
        if [ $? -eq 0 ]; then
            echo "✅ SSH rule created successfully!"
        else
            echo "❌ Failed to create SSH rule. You may need to create it manually."
        fi
    fi
else
    echo "⚠️  No Network Security Group found. You may need to configure firewall rules manually."
fi

echo
echo "=== Next Steps ==="
echo "1. Test SSH connection: ssh azureuser@$PUBLIC_IP_ADDRESS"
echo "2. If you can't connect, check your SSH key configuration"
echo "3. Ensure your local firewall allows outbound SSH connections"
echo "4. You can now use this VM as a jump server to access your AKS cluster"

echo
echo "🚀 Your ubuntu-jump VM is now accessible via public IP: $PUBLIC_IP_ADDRESS"
