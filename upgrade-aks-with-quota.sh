#!/bin/bash

echo "=== Upgrade AKS Node with Available Quota ==="

# Variables
AKS_CLUSTER_NAME="myAKSCluster"
AKS_RESOURCE_GROUP="myResourceGroup"
OLD_NODE_POOL_NAME="nodepool1"
NEW_NODE_POOL_NAME="nodepool2"
NEW_VM_SIZE="Standard_D4s_v3"

echo "AKS Cluster: $AKS_CLUSTER_NAME"
echo "AKS Resource Group: $AKS_RESOURCE_GROUP"
echo "New VM Size: $NEW_VM_SIZE (4 vCPUs, 16 GB RAM)"
echo

# Show current quota usage
echo "=== Current vCPU Quota Usage ==="
az vm list-usage --location eastus --query "[?contains(name.value, 'cores')].{Name:name.localizedValue, Current:currentValue, Limit:limit}" -o table

echo
echo "🎉 Great! You now have 10 vCPU quota limit!"
echo "Current usage: 4/10 vCPUs - We have enough quota to upgrade!"

# Show current AKS configuration
echo
echo "=== Current AKS Configuration ==="
az aks show --resource-group "$AKS_RESOURCE_GROUP" --name "$AKS_CLUSTER_NAME" --query "agentPoolProfiles[].{Name:name, Count:count, VmSize:vmSize, Mode:mode}" -o table

echo
echo "=== Upgrade Plan ==="
echo "1. Create new node pool with Standard_D4s_v3 (4 vCPUs)"
echo "2. Wait for new node to be ready"
echo "3. Delete old node pool with Standard_D2s_v3 (2 vCPUs)"
echo "4. Result: 2x CPU performance improvement"
echo

read -p "Do you want to proceed with the AKS upgrade? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Operation cancelled."
    exit 1
fi

echo
echo "🚀 Starting AKS node pool upgrade..."

# Step 1: Create new node pool with larger VM size
echo
echo "Step 1: Creating new node pool with Standard_D4s_v3..."
az aks nodepool add \
    --resource-group "$AKS_RESOURCE_GROUP" \
    --cluster-name "$AKS_CLUSTER_NAME" \
    --name "$NEW_NODE_POOL_NAME" \
    --node-count 1 \
    --node-vm-size "$NEW_VM_SIZE" \
    --mode System

if [ $? -ne 0 ]; then
    echo "❌ Failed to create new node pool!"
    exit 1
fi

echo "✅ New node pool created successfully!"

# Step 2: Wait for new node pool to be ready
echo
echo "Step 2: Waiting for new node pool to be ready..."
echo "This may take 3-5 minutes..."

# Wait and check node status
for i in {1..10}; do
    echo "Checking node status... ($i/10)"
    sleep 30
    
    # Check if new node is ready
    NEW_NODE_READY=$(kubectl get nodes --no-headers | grep -c "Ready")
    if [ "$NEW_NODE_READY" -ge 2 ]; then
        echo "✅ New node is ready!"
        break
    fi
    
    if [ $i -eq 10 ]; then
        echo "⚠️  New node is taking longer than expected, but continuing..."
    fi
done

# Show current nodes
echo
echo "Current nodes:"
kubectl get nodes -o wide

# Step 3: Delete old node pool
echo
echo "Step 3: Deleting old node pool..."
az aks nodepool delete \
    --resource-group "$AKS_RESOURCE_GROUP" \
    --cluster-name "$AKS_CLUSTER_NAME" \
    --name "$OLD_NODE_POOL_NAME"

if [ $? -ne 0 ]; then
    echo "⚠️  Warning: Failed to delete old node pool automatically."
    echo "You may need to delete it manually later:"
    echo "az aks nodepool delete --resource-group $AKS_RESOURCE_GROUP --cluster-name $AKS_CLUSTER_NAME --name $OLD_NODE_POOL_NAME"
else
    echo "✅ Old node pool deleted successfully!"
fi

# Step 4: Wait for changes to propagate
echo
echo "Step 4: Waiting for changes to propagate..."
sleep 60

# Show final status
echo
echo "=== Final Configuration ==="
echo
echo "AKS Cluster Status:"
az aks show --resource-group "$AKS_RESOURCE_GROUP" --name "$AKS_CLUSTER_NAME" --query "agentPoolProfiles[].{Name:name, Count:count, VmSize:vmSize, Mode:mode}" -o table

echo
echo "Node Status:"
kubectl get nodes -o wide

echo
echo "Final vCPU Usage:"
az vm list-usage --location eastus --query "[?contains(name.value, 'cores')].{Name:name.localizedValue, Current:currentValue, Limit:limit}" -o table

echo
echo "Pod Status (checking for any issues):"
kubectl get pods --all-namespaces --field-selector=status.phase!=Running | head -5

echo
echo "🎉 SUCCESS! AKS node upgraded successfully!"
echo
echo "=== Upgrade Summary ==="
echo "✅ Old VM Size: Standard_D2s_v3 (2 vCPUs, 8 GB RAM)"
echo "✅ New VM Size: Standard_D4s_v3 (4 vCPUs, 16 GB RAM)"
echo "✅ CPU Performance: 2x improvement"
echo "✅ Memory: 2x improvement"
echo "✅ Node Pool: $NEW_NODE_POOL_NAME"
echo
echo "=== Performance Testing ==="
echo "Test your web services to see the improvement:"
echo "1. curl -I https://nginx.seoedge.pro"
echo "2. curl -I https://jenkins.seoedge.pro"
echo "3. curl -I https://apach2.seoedge.pro"
echo
echo "Monitor resource usage:"
echo "kubectl top nodes"
echo "kubectl top pods --all-namespaces"
