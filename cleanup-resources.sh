#!/bin/bash

echo "Cleaning up existing VM resources..."

# Delete VM if it exists
echo "Checking for existing VM..."
if az vm show --resource-group myResourceGroup --name ubuntu-test-vm &> /dev/null; then
    echo "Deleting VM..."
    az vm delete --resource-group myResourceGroup --name ubuntu-test-vm --yes
fi

# Delete NIC
echo "Deleting network interface..."
if az network nic show --resource-group myResourceGroup --name ubuntu-test-vm-nic &> /dev/null; then
    az network nic delete --resource-group myResourceGroup --name ubuntu-test-vm-nic
fi

# Delete Public IP
echo "Deleting public IP..."
if az network public-ip show --resource-group myResourceGroup --name ubuntu-test-vm-ip &> /dev/null; then
    az network public-ip delete --resource-group myResourceGroup --name ubuntu-test-vm-ip
fi

# Delete NSG
echo "Deleting network security group..."
if az network nsg show --resource-group myResourceGroup --name ubuntu-test-vm-nsg &> /dev/null; then
    az network nsg delete --resource-group myResourceGroup --name ubuntu-test-vm-nsg
fi

echo "Cleanup completed!"
